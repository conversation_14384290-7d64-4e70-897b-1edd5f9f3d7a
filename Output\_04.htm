<!doctype html public "-//w3c//dtd html 4.0 transitional//en">
<html><head>
<title>Static Call Graph - [.\Output\_04.axf]</title></head>
<body><HR>
<H1>Static Call Graph for image .\Output\_04.axf</H1><HR>
<BR><P>#&#060CALLGRAPH&#062# ARM Linker, 5060960: Last Updated: Sat Aug 02 04:50:05 2025
<BR><P>
<H3>Maximum Stack Usage =        632 bytes + Unknown(Functions without stacksize, Cycles, Untraceable Function Pointers)</H3><H3>
Call chain for Maximum Stack Depth:</H3>
User_main &rArr; MenuHaddler_1 &rArr; PS2_ReadNum &rArr; OS_Num_Show &rArr; OS_String_Show &rArr; OS_Font_Show &rArr; OS_HzMat_Get &rArr; W25Q64_Read &rArr; SPI1_ReadWriteByte
<P>
<H3>
Functions with no stack information
</H3><UL>
 <LI><a href="#[9c]">__user_initial_stackheap</a>
 <LI><a href="#[101]">OS_CPU_SR_Save</a>
 <LI><a href="#[103]">OS_CPU_SR_Restore</a>
 <LI><a href="#[114]">OSCtxSw</a>
 <LI><a href="#[119]">OSStartHighRdy</a>
 <LI><a href="#[c]">PendSV_Handler</a>
</UL>
</UL>
<P>
<H3>
Mutually Recursive functions
</H3> <LI><a href="#[20]">ADC_IRQHandler</a>&nbsp;&nbsp;&nbsp;&rArr;&nbsp;&nbsp;&nbsp;<a href="#[20]">ADC_IRQHandler</a><BR>
</UL>
<P>
<H3>
Function Pointers
</H3><UL>
 <LI><a href="#[20]">ADC_IRQHandler</a> from startup_stm32f40_41xxx.o(.text) referenced from startup_stm32f40_41xxx.o(RESET)
 <LI><a href="#[8]">BusFault_Handler</a> from stm32f4xx_it.o(i.BusFault_Handler) referenced from startup_stm32f40_41xxx.o(RESET)
 <LI><a href="#[22]">CAN1_RX0_IRQHandler</a> from startup_stm32f40_41xxx.o(.text) referenced from startup_stm32f40_41xxx.o(RESET)
 <LI><a href="#[23]">CAN1_RX1_IRQHandler</a> from startup_stm32f40_41xxx.o(.text) referenced from startup_stm32f40_41xxx.o(RESET)
 <LI><a href="#[24]">CAN1_SCE_IRQHandler</a> from startup_stm32f40_41xxx.o(.text) referenced from startup_stm32f40_41xxx.o(RESET)
 <LI><a href="#[21]">CAN1_TX_IRQHandler</a> from startup_stm32f40_41xxx.o(.text) referenced from startup_stm32f40_41xxx.o(RESET)
 <LI><a href="#[4e]">CAN2_RX0_IRQHandler</a> from startup_stm32f40_41xxx.o(.text) referenced from startup_stm32f40_41xxx.o(RESET)
 <LI><a href="#[4f]">CAN2_RX1_IRQHandler</a> from startup_stm32f40_41xxx.o(.text) referenced from startup_stm32f40_41xxx.o(RESET)
 <LI><a href="#[50]">CAN2_SCE_IRQHandler</a> from startup_stm32f40_41xxx.o(.text) referenced from startup_stm32f40_41xxx.o(RESET)
 <LI><a href="#[4d]">CAN2_TX_IRQHandler</a> from startup_stm32f40_41xxx.o(.text) referenced from startup_stm32f40_41xxx.o(RESET)
 <LI><a href="#[5d]">CRYP_IRQHandler</a> from startup_stm32f40_41xxx.o(.text) referenced from startup_stm32f40_41xxx.o(RESET)
 <LI><a href="#[5c]">DCMI_IRQHandler</a> from startup_stm32f40_41xxx.o(.text) referenced from startup_stm32f40_41xxx.o(RESET)
 <LI><a href="#[19]">DMA1_Stream0_IRQHandler</a> from startup_stm32f40_41xxx.o(.text) referenced from startup_stm32f40_41xxx.o(RESET)
 <LI><a href="#[1a]">DMA1_Stream1_IRQHandler</a> from startup_stm32f40_41xxx.o(.text) referenced from startup_stm32f40_41xxx.o(RESET)
 <LI><a href="#[1b]">DMA1_Stream2_IRQHandler</a> from startup_stm32f40_41xxx.o(.text) referenced from startup_stm32f40_41xxx.o(RESET)
 <LI><a href="#[1c]">DMA1_Stream3_IRQHandler</a> from startup_stm32f40_41xxx.o(.text) referenced from startup_stm32f40_41xxx.o(RESET)
 <LI><a href="#[1d]">DMA1_Stream4_IRQHandler</a> from startup_stm32f40_41xxx.o(.text) referenced from startup_stm32f40_41xxx.o(RESET)
 <LI><a href="#[1e]">DMA1_Stream5_IRQHandler</a> from startup_stm32f40_41xxx.o(.text) referenced from startup_stm32f40_41xxx.o(RESET)
 <LI><a href="#[1f]">DMA1_Stream6_IRQHandler</a> from startup_stm32f40_41xxx.o(.text) referenced from startup_stm32f40_41xxx.o(RESET)
 <LI><a href="#[3d]">DMA1_Stream7_IRQHandler</a> from startup_stm32f40_41xxx.o(.text) referenced from startup_stm32f40_41xxx.o(RESET)
 <LI><a href="#[46]">DMA2_Stream0_IRQHandler</a> from user_adc.o(i.DMA2_Stream0_IRQHandler) referenced from startup_stm32f40_41xxx.o(RESET)
 <LI><a href="#[47]">DMA2_Stream1_IRQHandler</a> from startup_stm32f40_41xxx.o(.text) referenced from startup_stm32f40_41xxx.o(RESET)
 <LI><a href="#[48]">DMA2_Stream2_IRQHandler</a> from startup_stm32f40_41xxx.o(.text) referenced from startup_stm32f40_41xxx.o(RESET)
 <LI><a href="#[49]">DMA2_Stream3_IRQHandler</a> from startup_stm32f40_41xxx.o(.text) referenced from startup_stm32f40_41xxx.o(RESET)
 <LI><a href="#[4a]">DMA2_Stream4_IRQHandler</a> from startup_stm32f40_41xxx.o(.text) referenced from startup_stm32f40_41xxx.o(RESET)
 <LI><a href="#[52]">DMA2_Stream5_IRQHandler</a> from startup_stm32f40_41xxx.o(.text) referenced from startup_stm32f40_41xxx.o(RESET)
 <LI><a href="#[53]">DMA2_Stream6_IRQHandler</a> from startup_stm32f40_41xxx.o(.text) referenced from startup_stm32f40_41xxx.o(RESET)
 <LI><a href="#[54]">DMA2_Stream7_IRQHandler</a> from startup_stm32f40_41xxx.o(.text) referenced from startup_stm32f40_41xxx.o(RESET)
 <LI><a href="#[b]">DebugMon_Handler</a> from stm32f4xx_it.o(i.DebugMon_Handler) referenced from startup_stm32f40_41xxx.o(RESET)
 <LI><a href="#[4b]">ETH_IRQHandler</a> from startup_stm32f40_41xxx.o(.text) referenced from startup_stm32f40_41xxx.o(RESET)
 <LI><a href="#[4c]">ETH_WKUP_IRQHandler</a> from startup_stm32f40_41xxx.o(.text) referenced from startup_stm32f40_41xxx.o(RESET)
 <LI><a href="#[14]">EXTI0_IRQHandler</a> from startup_stm32f40_41xxx.o(.text) referenced from startup_stm32f40_41xxx.o(RESET)
 <LI><a href="#[36]">EXTI15_10_IRQHandler</a> from startup_stm32f40_41xxx.o(.text) referenced from startup_stm32f40_41xxx.o(RESET)
 <LI><a href="#[15]">EXTI1_IRQHandler</a> from user_adc.o(i.EXTI1_IRQHandler) referenced from startup_stm32f40_41xxx.o(RESET)
 <LI><a href="#[16]">EXTI2_IRQHandler</a> from startup_stm32f40_41xxx.o(.text) referenced from startup_stm32f40_41xxx.o(RESET)
 <LI><a href="#[17]">EXTI3_IRQHandler</a> from startup_stm32f40_41xxx.o(.text) referenced from startup_stm32f40_41xxx.o(RESET)
 <LI><a href="#[18]">EXTI4_IRQHandler</a> from startup_stm32f40_41xxx.o(.text) referenced from startup_stm32f40_41xxx.o(RESET)
 <LI><a href="#[25]">EXTI9_5_IRQHandler</a> from startup_stm32f40_41xxx.o(.text) referenced from startup_stm32f40_41xxx.o(RESET)
 <LI><a href="#[12]">FLASH_IRQHandler</a> from startup_stm32f40_41xxx.o(.text) referenced from startup_stm32f40_41xxx.o(RESET)
 <LI><a href="#[5f]">FPU_IRQHandler</a> from startup_stm32f40_41xxx.o(.text) referenced from startup_stm32f40_41xxx.o(RESET)
 <LI><a href="#[3e]">FSMC_IRQHandler</a> from startup_stm32f40_41xxx.o(.text) referenced from startup_stm32f40_41xxx.o(RESET)
 <LI><a href="#[5e]">HASH_RNG_IRQHandler</a> from startup_stm32f40_41xxx.o(.text) referenced from startup_stm32f40_41xxx.o(RESET)
 <LI><a href="#[6]">HardFault_Handler</a> from stm32f4xx_it.o(i.HardFault_Handler) referenced from startup_stm32f40_41xxx.o(RESET)
 <LI><a href="#[2e]">I2C1_ER_IRQHandler</a> from startup_stm32f40_41xxx.o(.text) referenced from startup_stm32f40_41xxx.o(RESET)
 <LI><a href="#[2d]">I2C1_EV_IRQHandler</a> from startup_stm32f40_41xxx.o(.text) referenced from startup_stm32f40_41xxx.o(RESET)
 <LI><a href="#[30]">I2C2_ER_IRQHandler</a> from startup_stm32f40_41xxx.o(.text) referenced from startup_stm32f40_41xxx.o(RESET)
 <LI><a href="#[2f]">I2C2_EV_IRQHandler</a> from startup_stm32f40_41xxx.o(.text) referenced from startup_stm32f40_41xxx.o(RESET)
 <LI><a href="#[57]">I2C3_ER_IRQHandler</a> from startup_stm32f40_41xxx.o(.text) referenced from startup_stm32f40_41xxx.o(RESET)
 <LI><a href="#[56]">I2C3_EV_IRQHandler</a> from startup_stm32f40_41xxx.o(.text) referenced from startup_stm32f40_41xxx.o(RESET)
 <LI><a href="#[68]">LED_main</a> from app_led.o(i.LED_main) referenced from main.o(i.main)
 <LI><a href="#[7]">MemManage_Handler</a> from stm32f4xx_it.o(i.MemManage_Handler) referenced from startup_stm32f40_41xxx.o(RESET)
 <LI><a href="#[67]">MyPs2KeyScan</a> from drive_ps2.o(i.MyPs2KeyScan) referenced from main.o(i.main)
 <LI><a href="#[5]">NMI_Handler</a> from stm32f4xx_it.o(i.NMI_Handler) referenced from startup_stm32f40_41xxx.o(RESET)
 <LI><a href="#[64]">OS_IDLE_Task</a> from os_cpu.o(i.OS_IDLE_Task) referenced from os_cpu.o(i.OS_Start)
 <LI><a href="#[51]">OTG_FS_IRQHandler</a> from startup_stm32f40_41xxx.o(.text) referenced from startup_stm32f40_41xxx.o(RESET)
 <LI><a href="#[38]">OTG_FS_WKUP_IRQHandler</a> from startup_stm32f40_41xxx.o(.text) referenced from startup_stm32f40_41xxx.o(RESET)
 <LI><a href="#[59]">OTG_HS_EP1_IN_IRQHandler</a> from startup_stm32f40_41xxx.o(.text) referenced from startup_stm32f40_41xxx.o(RESET)
 <LI><a href="#[58]">OTG_HS_EP1_OUT_IRQHandler</a> from startup_stm32f40_41xxx.o(.text) referenced from startup_stm32f40_41xxx.o(RESET)
 <LI><a href="#[5b]">OTG_HS_IRQHandler</a> from startup_stm32f40_41xxx.o(.text) referenced from startup_stm32f40_41xxx.o(RESET)
 <LI><a href="#[5a]">OTG_HS_WKUP_IRQHandler</a> from startup_stm32f40_41xxx.o(.text) referenced from startup_stm32f40_41xxx.o(RESET)
 <LI><a href="#[f]">PVD_IRQHandler</a> from startup_stm32f40_41xxx.o(.text) referenced from startup_stm32f40_41xxx.o(RESET)
 <LI><a href="#[c]">PendSV_Handler</a> from core.o(CODE) referenced from startup_stm32f40_41xxx.o(RESET)
 <LI><a href="#[13]">RCC_IRQHandler</a> from startup_stm32f40_41xxx.o(.text) referenced from startup_stm32f40_41xxx.o(RESET)
 <LI><a href="#[37]">RTC_Alarm_IRQHandler</a> from startup_stm32f40_41xxx.o(.text) referenced from startup_stm32f40_41xxx.o(RESET)
 <LI><a href="#[11]">RTC_WKUP_IRQHandler</a> from startup_stm32f40_41xxx.o(.text) referenced from startup_stm32f40_41xxx.o(RESET)
 <LI><a href="#[4]">Reset_Handler</a> from startup_stm32f40_41xxx.o(.text) referenced from startup_stm32f40_41xxx.o(RESET)
 <LI><a href="#[3f]">SDIO_IRQHandler</a> from startup_stm32f40_41xxx.o(.text) referenced from startup_stm32f40_41xxx.o(RESET)
 <LI><a href="#[31]">SPI1_IRQHandler</a> from startup_stm32f40_41xxx.o(.text) referenced from startup_stm32f40_41xxx.o(RESET)
 <LI><a href="#[32]">SPI2_IRQHandler</a> from startup_stm32f40_41xxx.o(.text) referenced from startup_stm32f40_41xxx.o(RESET)
 <LI><a href="#[41]">SPI3_IRQHandler</a> from startup_stm32f40_41xxx.o(.text) referenced from startup_stm32f40_41xxx.o(RESET)
 <LI><a href="#[a]">SVC_Handler</a> from stm32f4xx_it.o(i.SVC_Handler) referenced from startup_stm32f40_41xxx.o(RESET)
 <LI><a href="#[d]">SysTick_Handler</a> from os_cpu.o(i.SysTick_Handler) referenced from startup_stm32f40_41xxx.o(RESET)
 <LI><a href="#[60]">SystemInit</a> from system_stm32f4xx.o(i.SystemInit) referenced from startup_stm32f40_41xxx.o(.text)
 <LI><a href="#[10]">TAMP_STAMP_IRQHandler</a> from startup_stm32f40_41xxx.o(.text) referenced from startup_stm32f40_41xxx.o(RESET)
 <LI><a href="#[26]">TIM1_BRK_TIM9_IRQHandler</a> from startup_stm32f40_41xxx.o(.text) referenced from startup_stm32f40_41xxx.o(RESET)
 <LI><a href="#[29]">TIM1_CC_IRQHandler</a> from startup_stm32f40_41xxx.o(.text) referenced from startup_stm32f40_41xxx.o(RESET)
 <LI><a href="#[28]">TIM1_TRG_COM_TIM11_IRQHandler</a> from startup_stm32f40_41xxx.o(.text) referenced from startup_stm32f40_41xxx.o(RESET)
 <LI><a href="#[27]">TIM1_UP_TIM10_IRQHandler</a> from startup_stm32f40_41xxx.o(.text) referenced from startup_stm32f40_41xxx.o(RESET)
 <LI><a href="#[2a]">TIM2_IRQHandler</a> from startup_stm32f40_41xxx.o(.text) referenced from startup_stm32f40_41xxx.o(RESET)
 <LI><a href="#[2b]">TIM3_IRQHandler</a> from startup_stm32f40_41xxx.o(.text) referenced from startup_stm32f40_41xxx.o(RESET)
 <LI><a href="#[2c]">TIM4_IRQHandler</a> from startup_stm32f40_41xxx.o(.text) referenced from startup_stm32f40_41xxx.o(RESET)
 <LI><a href="#[40]">TIM5_IRQHandler</a> from startup_stm32f40_41xxx.o(.text) referenced from startup_stm32f40_41xxx.o(RESET)
 <LI><a href="#[44]">TIM6_DAC_IRQHandler</a> from startup_stm32f40_41xxx.o(.text) referenced from startup_stm32f40_41xxx.o(RESET)
 <LI><a href="#[45]">TIM7_IRQHandler</a> from startup_stm32f40_41xxx.o(.text) referenced from startup_stm32f40_41xxx.o(RESET)
 <LI><a href="#[39]">TIM8_BRK_TIM12_IRQHandler</a> from startup_stm32f40_41xxx.o(.text) referenced from startup_stm32f40_41xxx.o(RESET)
 <LI><a href="#[3c]">TIM8_CC_IRQHandler</a> from startup_stm32f40_41xxx.o(.text) referenced from startup_stm32f40_41xxx.o(RESET)
 <LI><a href="#[3b]">TIM8_TRG_COM_TIM14_IRQHandler</a> from startup_stm32f40_41xxx.o(.text) referenced from startup_stm32f40_41xxx.o(RESET)
 <LI><a href="#[3a]">TIM8_UP_TIM13_IRQHandler</a> from startup_stm32f40_41xxx.o(.text) referenced from startup_stm32f40_41xxx.o(RESET)
 <LI><a href="#[65]">Task_End</a> from os_cpu.o(i.Task_End) referenced from os_cpu.o(i.Task_Create)
 <LI><a href="#[42]">UART4_IRQHandler</a> from startup_stm32f40_41xxx.o(.text) referenced from startup_stm32f40_41xxx.o(RESET)
 <LI><a href="#[43]">UART5_IRQHandler</a> from startup_stm32f40_41xxx.o(.text) referenced from startup_stm32f40_41xxx.o(RESET)
 <LI><a href="#[33]">USART1_IRQHandler</a> from usart.o(i.USART1_IRQHandler) referenced from startup_stm32f40_41xxx.o(RESET)
 <LI><a href="#[34]">USART2_IRQHandler</a> from startup_stm32f40_41xxx.o(.text) referenced from startup_stm32f40_41xxx.o(RESET)
 <LI><a href="#[35]">USART3_IRQHandler</a> from startup_stm32f40_41xxx.o(.text) referenced from startup_stm32f40_41xxx.o(RESET)
 <LI><a href="#[55]">USART6_IRQHandler</a> from drive_communication.o(i.USART6_IRQHandler) referenced from startup_stm32f40_41xxx.o(RESET)
 <LI><a href="#[9]">UsageFault_Handler</a> from stm32f4xx_it.o(i.UsageFault_Handler) referenced from startup_stm32f40_41xxx.o(RESET)
 <LI><a href="#[66]">User_main</a> from user.o(i.User_main) referenced from main.o(i.main)
 <LI><a href="#[e]">WWDG_IRQHandler</a> from startup_stm32f40_41xxx.o(.text) referenced from startup_stm32f40_41xxx.o(RESET)
 <LI><a href="#[69]">__main</a> from __main.o(!!!main) referenced from startup_stm32f40_41xxx.o(.text)
 <LI><a href="#[63]">_printf_input_char</a> from _printf_char_common.o(.text) referenced from _printf_char_common.o(.text)
 <LI><a href="#[62]">_sputc</a> from _sputc.o(.text) referenced from noretval__2sprintf.o(.text)
</UL>
<P>
<H3>
Global Symbols
</H3>
<P><STRONG><a name="[69]"></a>__main</STRONG> (Thumb, 8 bytes, Stack size 0 bytes, __main.o(!!!main))
<BR><BR>[Calls]<UL><LI><a href="#[6b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__rt_entry
<LI><a href="#[6a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__scatterload
</UL>

<P><STRONG><a name="[6a]"></a>__scatterload</STRONG> (Thumb, 0 bytes, Stack size unknown bytes, __scatter.o(!!!scatter))
<BR><BR>[Called By]<UL><LI><a href="#[69]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__main
</UL>

<P><STRONG><a name="[6c]"></a>__scatterload_rt2</STRONG> (Thumb, 44 bytes, Stack size unknown bytes, __scatter.o(!!!scatter), UNUSED)
<BR><BR>[Calls]<UL><LI><a href="#[6b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__rt_entry
</UL>

<P><STRONG><a name="[170]"></a>__scatterload_rt2_thumb_only</STRONG> (Thumb, 0 bytes, Stack size unknown bytes, __scatter.o(!!!scatter), UNUSED)

<P><STRONG><a name="[171]"></a>__scatterload_null</STRONG> (Thumb, 0 bytes, Stack size unknown bytes, __scatter.o(!!!scatter), UNUSED)

<P><STRONG><a name="[6d]"></a>__scatterload_copy</STRONG> (Thumb, 26 bytes, Stack size unknown bytes, __scatter_copy.o(!!handler_copy), UNUSED)
<BR><BR>[Calls]<UL><LI><a href="#[6d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__scatterload_copy
</UL>
<BR>[Called By]<UL><LI><a href="#[6d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__scatterload_copy
</UL>

<P><STRONG><a name="[172]"></a>__scatterload_zeroinit</STRONG> (Thumb, 28 bytes, Stack size unknown bytes, __scatter_zi.o(!!handler_zi), UNUSED)

<P><STRONG><a name="[6e]"></a>_printf_f</STRONG> (Thumb, 0 bytes, Stack size unknown bytes, _printf_f.o(.ARM.Collect$$_printf_percent$$00000003))
<BR><BR>[Stack]<UL><LI>Max Depth = 324 + Unknown Stack Size
<LI>Call Chain = _printf_f &rArr; _printf_fp_dec &rArr; _printf_fp_dec_real &rArr; _fp_digits &rArr; _btod_etento &rArr; _btod_emul &rArr; _e2e
</UL>
<BR>[Calls]<UL><LI><a href="#[6f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_printf_fp_dec
</UL>

<P><STRONG><a name="[8b]"></a>_printf_percent</STRONG> (Thumb, 0 bytes, Stack size unknown bytes, _printf_percent.o(.ARM.Collect$$_printf_percent$$00000000))
<BR><BR>[Called By]<UL><LI><a href="#[89]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__printf
</UL>

<P><STRONG><a name="[70]"></a>_printf_e</STRONG> (Thumb, 0 bytes, Stack size unknown bytes, _printf_e.o(.ARM.Collect$$_printf_percent$$00000004))
<BR><BR>[Stack]<UL><LI>Max Depth = 324 + Unknown Stack Size
<LI>Call Chain = _printf_e &rArr; _printf_fp_dec &rArr; _printf_fp_dec_real &rArr; _fp_digits &rArr; _btod_etento &rArr; _btod_emul &rArr; _e2e
</UL>
<BR>[Calls]<UL><LI><a href="#[6f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_printf_fp_dec
</UL>

<P><STRONG><a name="[71]"></a>_printf_g</STRONG> (Thumb, 0 bytes, Stack size unknown bytes, _printf_g.o(.ARM.Collect$$_printf_percent$$00000005))
<BR><BR>[Stack]<UL><LI>Max Depth = 324 + Unknown Stack Size
<LI>Call Chain = _printf_g &rArr; _printf_fp_dec &rArr; _printf_fp_dec_real &rArr; _fp_digits &rArr; _btod_etento &rArr; _btod_emul &rArr; _e2e
</UL>
<BR>[Calls]<UL><LI><a href="#[6f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_printf_fp_dec
</UL>

<P><STRONG><a name="[72]"></a>_printf_a</STRONG> (Thumb, 0 bytes, Stack size unknown bytes, _printf_a.o(.ARM.Collect$$_printf_percent$$00000006))
<BR><BR>[Stack]<UL><LI>Max Depth = 112 + Unknown Stack Size
<LI>Call Chain = _printf_a &rArr; _printf_fp_hex &rArr; _printf_fp_hex_real &rArr; _printf_fp_infnan &rArr; _printf_post_padding
</UL>
<BR>[Calls]<UL><LI><a href="#[73]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_printf_fp_hex
</UL>

<P><STRONG><a name="[173]"></a>_printf_percent_end</STRONG> (Thumb, 0 bytes, Stack size unknown bytes, _printf_percent_end.o(.ARM.Collect$$_printf_percent$$00000017))

<P><STRONG><a name="[7d]"></a>__rt_lib_init</STRONG> (Thumb, 0 bytes, Stack size unknown bytes, libinit.o(.ARM.Collect$$libinit$$00000000))
<BR><BR>[Called By]<UL><LI><a href="#[7c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__rt_entry_li
</UL>

<P><STRONG><a name="[74]"></a>__rt_lib_init_fp_1</STRONG> (Thumb, 0 bytes, Stack size unknown bytes, libinit2.o(.ARM.Collect$$libinit$$00000001))
<BR><BR>[Calls]<UL><LI><a href="#[75]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_fp_init
</UL>

<P><STRONG><a name="[174]"></a>__rt_lib_init_heap_1</STRONG> (Thumb, 0 bytes, Stack size unknown bytes, libinit2.o(.ARM.Collect$$libinit$$0000000A))

<P><STRONG><a name="[76]"></a>__rt_lib_init_lc_common</STRONG> (Thumb, 0 bytes, Stack size unknown bytes, libinit2.o(.ARM.Collect$$libinit$$0000000F))
<BR><BR>[Calls]<UL><LI><a href="#[77]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__rt_locale
</UL>

<P><STRONG><a name="[175]"></a>__rt_lib_init_preinit_1</STRONG> (Thumb, 0 bytes, Stack size unknown bytes, libinit2.o(.ARM.Collect$$libinit$$00000004))

<P><STRONG><a name="[176]"></a>__rt_lib_init_rand_1</STRONG> (Thumb, 0 bytes, Stack size unknown bytes, libinit2.o(.ARM.Collect$$libinit$$0000000E))

<P><STRONG><a name="[177]"></a>__rt_lib_init_user_alloc_1</STRONG> (Thumb, 0 bytes, Stack size unknown bytes, libinit2.o(.ARM.Collect$$libinit$$0000000C))

<P><STRONG><a name="[178]"></a>__rt_lib_init_lc_collate_1</STRONG> (Thumb, 0 bytes, Stack size unknown bytes, libinit2.o(.ARM.Collect$$libinit$$00000011))

<P><STRONG><a name="[179]"></a>__rt_lib_init_lc_ctype_1</STRONG> (Thumb, 0 bytes, Stack size unknown bytes, libinit2.o(.ARM.Collect$$libinit$$00000013))

<P><STRONG><a name="[17a]"></a>__rt_lib_init_lc_monetary_1</STRONG> (Thumb, 0 bytes, Stack size unknown bytes, libinit2.o(.ARM.Collect$$libinit$$00000015))

<P><STRONG><a name="[78]"></a>__rt_lib_init_lc_numeric_2</STRONG> (Thumb, 0 bytes, Stack size unknown bytes, libinit2.o(.ARM.Collect$$libinit$$00000016))
<BR><BR>[Stack]<UL><LI>Max Depth = 8 + Unknown Stack Size
<LI>Call Chain = __rt_lib_init_lc_numeric_2 &rArr; _get_lc_numeric
</UL>
<BR>[Calls]<UL><LI><a href="#[79]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_get_lc_numeric
</UL>

<P><STRONG><a name="[17b]"></a>__rt_lib_init_alloca_1</STRONG> (Thumb, 0 bytes, Stack size unknown bytes, libinit2.o(.ARM.Collect$$libinit$$0000002E))

<P><STRONG><a name="[17c]"></a>__rt_lib_init_argv_1</STRONG> (Thumb, 0 bytes, Stack size unknown bytes, libinit2.o(.ARM.Collect$$libinit$$0000002C))

<P><STRONG><a name="[17d]"></a>__rt_lib_init_atexit_1</STRONG> (Thumb, 0 bytes, Stack size unknown bytes, libinit2.o(.ARM.Collect$$libinit$$0000001B))

<P><STRONG><a name="[17e]"></a>__rt_lib_init_clock_1</STRONG> (Thumb, 0 bytes, Stack size unknown bytes, libinit2.o(.ARM.Collect$$libinit$$00000021))

<P><STRONG><a name="[17f]"></a>__rt_lib_init_cpp_1</STRONG> (Thumb, 0 bytes, Stack size unknown bytes, libinit2.o(.ARM.Collect$$libinit$$00000032))

<P><STRONG><a name="[180]"></a>__rt_lib_init_exceptions_1</STRONG> (Thumb, 0 bytes, Stack size unknown bytes, libinit2.o(.ARM.Collect$$libinit$$00000030))

<P><STRONG><a name="[181]"></a>__rt_lib_init_fp_trap_1</STRONG> (Thumb, 0 bytes, Stack size unknown bytes, libinit2.o(.ARM.Collect$$libinit$$0000001F))

<P><STRONG><a name="[182]"></a>__rt_lib_init_getenv_1</STRONG> (Thumb, 0 bytes, Stack size unknown bytes, libinit2.o(.ARM.Collect$$libinit$$00000023))

<P><STRONG><a name="[183]"></a>__rt_lib_init_lc_numeric_1</STRONG> (Thumb, 0 bytes, Stack size unknown bytes, libinit2.o(.ARM.Collect$$libinit$$00000017))

<P><STRONG><a name="[184]"></a>__rt_lib_init_lc_time_1</STRONG> (Thumb, 0 bytes, Stack size unknown bytes, libinit2.o(.ARM.Collect$$libinit$$00000019))

<P><STRONG><a name="[185]"></a>__rt_lib_init_return</STRONG> (Thumb, 0 bytes, Stack size unknown bytes, libinit2.o(.ARM.Collect$$libinit$$00000033))

<P><STRONG><a name="[186]"></a>__rt_lib_init_signal_1</STRONG> (Thumb, 0 bytes, Stack size unknown bytes, libinit2.o(.ARM.Collect$$libinit$$0000001D))

<P><STRONG><a name="[187]"></a>__rt_lib_init_stdio_1</STRONG> (Thumb, 0 bytes, Stack size unknown bytes, libinit2.o(.ARM.Collect$$libinit$$00000025))

<P><STRONG><a name="[82]"></a>__rt_lib_shutdown</STRONG> (Thumb, 0 bytes, Stack size unknown bytes, libshutdown.o(.ARM.Collect$$libshutdown$$00000000))
<BR><BR>[Called By]<UL><LI><a href="#[81]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__rt_exit_ls
</UL>

<P><STRONG><a name="[188]"></a>__rt_lib_shutdown_cpp_1</STRONG> (Thumb, 0 bytes, Stack size unknown bytes, libshutdown2.o(.ARM.Collect$$libshutdown$$00000004))

<P><STRONG><a name="[189]"></a>__rt_lib_shutdown_fini_1</STRONG> (Thumb, 0 bytes, Stack size unknown bytes, libshutdown2.o(.ARM.Collect$$libshutdown$$00000002))

<P><STRONG><a name="[18a]"></a>__rt_lib_shutdown_fp_trap_1</STRONG> (Thumb, 0 bytes, Stack size unknown bytes, libshutdown2.o(.ARM.Collect$$libshutdown$$00000009))

<P><STRONG><a name="[18b]"></a>__rt_lib_shutdown_heap_1</STRONG> (Thumb, 0 bytes, Stack size unknown bytes, libshutdown2.o(.ARM.Collect$$libshutdown$$00000011))

<P><STRONG><a name="[18c]"></a>__rt_lib_shutdown_return</STRONG> (Thumb, 0 bytes, Stack size unknown bytes, libshutdown2.o(.ARM.Collect$$libshutdown$$00000012))

<P><STRONG><a name="[18d]"></a>__rt_lib_shutdown_signal_1</STRONG> (Thumb, 0 bytes, Stack size unknown bytes, libshutdown2.o(.ARM.Collect$$libshutdown$$0000000C))

<P><STRONG><a name="[18e]"></a>__rt_lib_shutdown_stdio_1</STRONG> (Thumb, 0 bytes, Stack size unknown bytes, libshutdown2.o(.ARM.Collect$$libshutdown$$00000006))

<P><STRONG><a name="[18f]"></a>__rt_lib_shutdown_user_alloc_1</STRONG> (Thumb, 0 bytes, Stack size unknown bytes, libshutdown2.o(.ARM.Collect$$libshutdown$$0000000E))

<P><STRONG><a name="[6b]"></a>__rt_entry</STRONG> (Thumb, 0 bytes, Stack size unknown bytes, __rtentry.o(.ARM.Collect$$rtentry$$00000000))
<BR><BR>[Called By]<UL><LI><a href="#[69]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__main
<LI><a href="#[6c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__scatterload_rt2
</UL>

<P><STRONG><a name="[190]"></a>__rt_entry_presh_1</STRONG> (Thumb, 0 bytes, Stack size unknown bytes, __rtentry2.o(.ARM.Collect$$rtentry$$00000002))

<P><STRONG><a name="[7a]"></a>__rt_entry_sh</STRONG> (Thumb, 0 bytes, Stack size unknown bytes, __rtentry4.o(.ARM.Collect$$rtentry$$00000004))
<BR><BR>[Stack]<UL><LI>Max Depth = 8 + Unknown Stack Size
<LI>Call Chain = __rt_entry_sh &rArr; __user_setup_stackheap
</UL>
<BR>[Calls]<UL><LI><a href="#[7b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__user_setup_stackheap
</UL>

<P><STRONG><a name="[7c]"></a>__rt_entry_li</STRONG> (Thumb, 0 bytes, Stack size unknown bytes, __rtentry2.o(.ARM.Collect$$rtentry$$0000000A))
<BR><BR>[Calls]<UL><LI><a href="#[7d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__rt_lib_init
</UL>

<P><STRONG><a name="[191]"></a>__rt_entry_postsh_1</STRONG> (Thumb, 0 bytes, Stack size unknown bytes, __rtentry2.o(.ARM.Collect$$rtentry$$00000009))

<P><STRONG><a name="[7e]"></a>__rt_entry_main</STRONG> (Thumb, 0 bytes, Stack size unknown bytes, __rtentry2.o(.ARM.Collect$$rtentry$$0000000D))
<BR><BR>[Stack]<UL><LI>Max Depth = 336 + Unknown Stack Size
<LI>Call Chain = __rt_entry_main &rArr; main &rArr; OS_Init &rArr; OS_LCD_Init &rArr; OS_String_Show &rArr; OS_Font_Show &rArr; OS_HzMat_Get &rArr; W25Q64_Read &rArr; SPI1_ReadWriteByte
</UL>
<BR>[Calls]<UL><LI><a href="#[7f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;main
<LI><a href="#[80]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;exit
</UL>

<P><STRONG><a name="[192]"></a>__rt_entry_postli_1</STRONG> (Thumb, 0 bytes, Stack size unknown bytes, __rtentry2.o(.ARM.Collect$$rtentry$$0000000C))

<P><STRONG><a name="[9d]"></a>__rt_exit</STRONG> (Thumb, 0 bytes, Stack size unknown bytes, rtexit.o(.ARM.Collect$$rtexit$$00000000))
<BR><BR>[Called By]<UL><LI><a href="#[80]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;exit
</UL>

<P><STRONG><a name="[81]"></a>__rt_exit_ls</STRONG> (Thumb, 0 bytes, Stack size unknown bytes, rtexit2.o(.ARM.Collect$$rtexit$$00000003))
<BR><BR>[Calls]<UL><LI><a href="#[82]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__rt_lib_shutdown
</UL>

<P><STRONG><a name="[193]"></a>__rt_exit_prels_1</STRONG> (Thumb, 0 bytes, Stack size unknown bytes, rtexit2.o(.ARM.Collect$$rtexit$$00000002))

<P><STRONG><a name="[83]"></a>__rt_exit_exit</STRONG> (Thumb, 0 bytes, Stack size unknown bytes, rtexit2.o(.ARM.Collect$$rtexit$$00000004))
<BR><BR>[Calls]<UL><LI><a href="#[84]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_sys_exit
</UL>

<P><STRONG><a name="[4]"></a>Reset_Handler</STRONG> (Thumb, 8 bytes, Stack size 0 bytes, startup_stm32f40_41xxx.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f40_41xxx.o(RESET)
</UL>
<P><STRONG><a name="[20]"></a>ADC_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32f40_41xxx.o(.text))
<BR><BR>[Calls]<UL><LI><a href="#[20]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ADC_IRQHandler
</UL>
<BR>[Called By]<UL><LI><a href="#[20]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ADC_IRQHandler
</UL>
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f40_41xxx.o(RESET)
</UL>
<P><STRONG><a name="[22]"></a>CAN1_RX0_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32f40_41xxx.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f40_41xxx.o(RESET)
</UL>
<P><STRONG><a name="[23]"></a>CAN1_RX1_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32f40_41xxx.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f40_41xxx.o(RESET)
</UL>
<P><STRONG><a name="[24]"></a>CAN1_SCE_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32f40_41xxx.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f40_41xxx.o(RESET)
</UL>
<P><STRONG><a name="[21]"></a>CAN1_TX_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32f40_41xxx.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f40_41xxx.o(RESET)
</UL>
<P><STRONG><a name="[4e]"></a>CAN2_RX0_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32f40_41xxx.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f40_41xxx.o(RESET)
</UL>
<P><STRONG><a name="[4f]"></a>CAN2_RX1_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32f40_41xxx.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f40_41xxx.o(RESET)
</UL>
<P><STRONG><a name="[50]"></a>CAN2_SCE_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32f40_41xxx.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f40_41xxx.o(RESET)
</UL>
<P><STRONG><a name="[4d]"></a>CAN2_TX_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32f40_41xxx.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f40_41xxx.o(RESET)
</UL>
<P><STRONG><a name="[5d]"></a>CRYP_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32f40_41xxx.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f40_41xxx.o(RESET)
</UL>
<P><STRONG><a name="[5c]"></a>DCMI_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32f40_41xxx.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f40_41xxx.o(RESET)
</UL>
<P><STRONG><a name="[19]"></a>DMA1_Stream0_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32f40_41xxx.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f40_41xxx.o(RESET)
</UL>
<P><STRONG><a name="[1a]"></a>DMA1_Stream1_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32f40_41xxx.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f40_41xxx.o(RESET)
</UL>
<P><STRONG><a name="[1b]"></a>DMA1_Stream2_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32f40_41xxx.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f40_41xxx.o(RESET)
</UL>
<P><STRONG><a name="[1c]"></a>DMA1_Stream3_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32f40_41xxx.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f40_41xxx.o(RESET)
</UL>
<P><STRONG><a name="[1d]"></a>DMA1_Stream4_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32f40_41xxx.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f40_41xxx.o(RESET)
</UL>
<P><STRONG><a name="[1e]"></a>DMA1_Stream5_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32f40_41xxx.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f40_41xxx.o(RESET)
</UL>
<P><STRONG><a name="[1f]"></a>DMA1_Stream6_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32f40_41xxx.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f40_41xxx.o(RESET)
</UL>
<P><STRONG><a name="[3d]"></a>DMA1_Stream7_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32f40_41xxx.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f40_41xxx.o(RESET)
</UL>
<P><STRONG><a name="[47]"></a>DMA2_Stream1_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32f40_41xxx.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f40_41xxx.o(RESET)
</UL>
<P><STRONG><a name="[48]"></a>DMA2_Stream2_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32f40_41xxx.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f40_41xxx.o(RESET)
</UL>
<P><STRONG><a name="[49]"></a>DMA2_Stream3_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32f40_41xxx.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f40_41xxx.o(RESET)
</UL>
<P><STRONG><a name="[4a]"></a>DMA2_Stream4_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32f40_41xxx.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f40_41xxx.o(RESET)
</UL>
<P><STRONG><a name="[52]"></a>DMA2_Stream5_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32f40_41xxx.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f40_41xxx.o(RESET)
</UL>
<P><STRONG><a name="[53]"></a>DMA2_Stream6_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32f40_41xxx.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f40_41xxx.o(RESET)
</UL>
<P><STRONG><a name="[54]"></a>DMA2_Stream7_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32f40_41xxx.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f40_41xxx.o(RESET)
</UL>
<P><STRONG><a name="[4b]"></a>ETH_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32f40_41xxx.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f40_41xxx.o(RESET)
</UL>
<P><STRONG><a name="[4c]"></a>ETH_WKUP_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32f40_41xxx.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f40_41xxx.o(RESET)
</UL>
<P><STRONG><a name="[14]"></a>EXTI0_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32f40_41xxx.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f40_41xxx.o(RESET)
</UL>
<P><STRONG><a name="[36]"></a>EXTI15_10_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32f40_41xxx.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f40_41xxx.o(RESET)
</UL>
<P><STRONG><a name="[16]"></a>EXTI2_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32f40_41xxx.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f40_41xxx.o(RESET)
</UL>
<P><STRONG><a name="[17]"></a>EXTI3_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32f40_41xxx.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f40_41xxx.o(RESET)
</UL>
<P><STRONG><a name="[18]"></a>EXTI4_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32f40_41xxx.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f40_41xxx.o(RESET)
</UL>
<P><STRONG><a name="[25]"></a>EXTI9_5_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32f40_41xxx.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f40_41xxx.o(RESET)
</UL>
<P><STRONG><a name="[12]"></a>FLASH_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32f40_41xxx.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f40_41xxx.o(RESET)
</UL>
<P><STRONG><a name="[5f]"></a>FPU_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32f40_41xxx.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f40_41xxx.o(RESET)
</UL>
<P><STRONG><a name="[3e]"></a>FSMC_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32f40_41xxx.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f40_41xxx.o(RESET)
</UL>
<P><STRONG><a name="[5e]"></a>HASH_RNG_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32f40_41xxx.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f40_41xxx.o(RESET)
</UL>
<P><STRONG><a name="[2e]"></a>I2C1_ER_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32f40_41xxx.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f40_41xxx.o(RESET)
</UL>
<P><STRONG><a name="[2d]"></a>I2C1_EV_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32f40_41xxx.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f40_41xxx.o(RESET)
</UL>
<P><STRONG><a name="[30]"></a>I2C2_ER_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32f40_41xxx.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f40_41xxx.o(RESET)
</UL>
<P><STRONG><a name="[2f]"></a>I2C2_EV_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32f40_41xxx.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f40_41xxx.o(RESET)
</UL>
<P><STRONG><a name="[57]"></a>I2C3_ER_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32f40_41xxx.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f40_41xxx.o(RESET)
</UL>
<P><STRONG><a name="[56]"></a>I2C3_EV_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32f40_41xxx.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f40_41xxx.o(RESET)
</UL>
<P><STRONG><a name="[51]"></a>OTG_FS_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32f40_41xxx.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f40_41xxx.o(RESET)
</UL>
<P><STRONG><a name="[38]"></a>OTG_FS_WKUP_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32f40_41xxx.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f40_41xxx.o(RESET)
</UL>
<P><STRONG><a name="[59]"></a>OTG_HS_EP1_IN_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32f40_41xxx.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f40_41xxx.o(RESET)
</UL>
<P><STRONG><a name="[58]"></a>OTG_HS_EP1_OUT_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32f40_41xxx.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f40_41xxx.o(RESET)
</UL>
<P><STRONG><a name="[5b]"></a>OTG_HS_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32f40_41xxx.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f40_41xxx.o(RESET)
</UL>
<P><STRONG><a name="[5a]"></a>OTG_HS_WKUP_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32f40_41xxx.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f40_41xxx.o(RESET)
</UL>
<P><STRONG><a name="[f]"></a>PVD_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32f40_41xxx.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f40_41xxx.o(RESET)
</UL>
<P><STRONG><a name="[13]"></a>RCC_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32f40_41xxx.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f40_41xxx.o(RESET)
</UL>
<P><STRONG><a name="[37]"></a>RTC_Alarm_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32f40_41xxx.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f40_41xxx.o(RESET)
</UL>
<P><STRONG><a name="[11]"></a>RTC_WKUP_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32f40_41xxx.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f40_41xxx.o(RESET)
</UL>
<P><STRONG><a name="[3f]"></a>SDIO_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32f40_41xxx.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f40_41xxx.o(RESET)
</UL>
<P><STRONG><a name="[31]"></a>SPI1_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32f40_41xxx.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f40_41xxx.o(RESET)
</UL>
<P><STRONG><a name="[32]"></a>SPI2_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32f40_41xxx.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f40_41xxx.o(RESET)
</UL>
<P><STRONG><a name="[41]"></a>SPI3_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32f40_41xxx.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f40_41xxx.o(RESET)
</UL>
<P><STRONG><a name="[10]"></a>TAMP_STAMP_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32f40_41xxx.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f40_41xxx.o(RESET)
</UL>
<P><STRONG><a name="[26]"></a>TIM1_BRK_TIM9_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32f40_41xxx.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f40_41xxx.o(RESET)
</UL>
<P><STRONG><a name="[29]"></a>TIM1_CC_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32f40_41xxx.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f40_41xxx.o(RESET)
</UL>
<P><STRONG><a name="[28]"></a>TIM1_TRG_COM_TIM11_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32f40_41xxx.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f40_41xxx.o(RESET)
</UL>
<P><STRONG><a name="[27]"></a>TIM1_UP_TIM10_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32f40_41xxx.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f40_41xxx.o(RESET)
</UL>
<P><STRONG><a name="[2a]"></a>TIM2_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32f40_41xxx.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f40_41xxx.o(RESET)
</UL>
<P><STRONG><a name="[2b]"></a>TIM3_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32f40_41xxx.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f40_41xxx.o(RESET)
</UL>
<P><STRONG><a name="[2c]"></a>TIM4_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32f40_41xxx.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f40_41xxx.o(RESET)
</UL>
<P><STRONG><a name="[40]"></a>TIM5_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32f40_41xxx.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f40_41xxx.o(RESET)
</UL>
<P><STRONG><a name="[44]"></a>TIM6_DAC_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32f40_41xxx.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f40_41xxx.o(RESET)
</UL>
<P><STRONG><a name="[45]"></a>TIM7_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32f40_41xxx.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f40_41xxx.o(RESET)
</UL>
<P><STRONG><a name="[39]"></a>TIM8_BRK_TIM12_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32f40_41xxx.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f40_41xxx.o(RESET)
</UL>
<P><STRONG><a name="[3c]"></a>TIM8_CC_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32f40_41xxx.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f40_41xxx.o(RESET)
</UL>
<P><STRONG><a name="[3b]"></a>TIM8_TRG_COM_TIM14_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32f40_41xxx.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f40_41xxx.o(RESET)
</UL>
<P><STRONG><a name="[3a]"></a>TIM8_UP_TIM13_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32f40_41xxx.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f40_41xxx.o(RESET)
</UL>
<P><STRONG><a name="[42]"></a>UART4_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32f40_41xxx.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f40_41xxx.o(RESET)
</UL>
<P><STRONG><a name="[43]"></a>UART5_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32f40_41xxx.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f40_41xxx.o(RESET)
</UL>
<P><STRONG><a name="[34]"></a>USART2_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32f40_41xxx.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f40_41xxx.o(RESET)
</UL>
<P><STRONG><a name="[35]"></a>USART3_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32f40_41xxx.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f40_41xxx.o(RESET)
</UL>
<P><STRONG><a name="[e]"></a>WWDG_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32f40_41xxx.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f40_41xxx.o(RESET)
</UL>
<P><STRONG><a name="[9c]"></a>__user_initial_stackheap</STRONG> (Thumb, 0 bytes, Stack size unknown bytes, startup_stm32f40_41xxx.o(.text))
<BR><BR>[Called By]<UL><LI><a href="#[7b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__user_setup_stackheap
</UL>

<P><STRONG><a name="[c1]"></a>arm_biquad_cascade_df1_f32</STRONG> (Thumb, 314 bytes, Stack size 16 bytes, arm_biquad_cascade_df1_f32.o(.text))
<BR><BR>[Stack]<UL><LI>Max Depth = 16<LI>Call Chain = arm_biquad_cascade_df1_f32
</UL>
<BR>[Called By]<UL><LI><a href="#[46]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;DMA2_Stream0_IRQHandler
</UL>

<P><STRONG><a name="[85]"></a>arm_biquad_cascade_df1_init_f32</STRONG> (Thumb, 22 bytes, Stack size 16 bytes, arm_biquad_cascade_df1_init_f32.o(.text))
<BR><BR>[Stack]<UL><LI>Max Depth = 20<LI>Call Chain = arm_biquad_cascade_df1_init_f32 &rArr; __aeabi_memclr4
</UL>
<BR>[Calls]<UL><LI><a href="#[86]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_memclr4
</UL>
<BR>[Called By]<UL><LI><a href="#[d1]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;init_iir_filter
</UL>

<P><STRONG><a name="[194]"></a>__use_no_semihosting</STRONG> (Thumb, 2 bytes, Stack size 0 bytes, use_no_semi_2.o(.text), UNUSED)

<P><STRONG><a name="[87]"></a>__2sprintf</STRONG> (Thumb, 34 bytes, Stack size 32 bytes, noretval__2sprintf.o(.text))
<BR><BR>[Stack]<UL><LI>Max Depth = 136 + Unknown Stack Size
<LI>Call Chain = __2sprintf &rArr; _printf_char_common &rArr; __printf
</UL>
<BR>[Calls]<UL><LI><a href="#[62]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_sputc
<LI><a href="#[88]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_printf_char_common
</UL>
<BR>[Called By]<UL><LI><a href="#[111]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;OS_Num_Show
</UL>

<P><STRONG><a name="[98]"></a>_printf_pre_padding</STRONG> (Thumb, 44 bytes, Stack size 16 bytes, _printf_pad.o(.text))
<BR><BR>[Stack]<UL><LI>Max Depth = 16<LI>Call Chain = _printf_pre_padding
</UL>
<BR>[Called By]<UL><LI><a href="#[97]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_printf_fp_infnan
<LI><a href="#[9a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_printf_fp_hex_real
<LI><a href="#[95]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_printf_fp_dec_real
</UL>

<P><STRONG><a name="[99]"></a>_printf_post_padding</STRONG> (Thumb, 34 bytes, Stack size 16 bytes, _printf_pad.o(.text))
<BR><BR>[Stack]<UL><LI>Max Depth = 16<LI>Call Chain = _printf_post_padding
</UL>
<BR>[Called By]<UL><LI><a href="#[97]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_printf_fp_infnan
<LI><a href="#[9a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_printf_fp_hex_real
<LI><a href="#[95]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_printf_fp_dec_real
</UL>

<P><STRONG><a name="[89]"></a>__printf</STRONG> (Thumb, 388 bytes, Stack size 40 bytes, __printf_flags_ss_wp.o(.text))
<BR><BR>[Stack]<UL><LI>Max Depth = 40 + Unknown Stack Size
<LI>Call Chain = __printf
</UL>
<BR>[Calls]<UL><LI><a href="#[8b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_printf_percent
<LI><a href="#[8a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_is_digit
</UL>
<BR>[Called By]<UL><LI><a href="#[88]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_printf_char_common
</UL>

<P><STRONG><a name="[a4]"></a>__aeabi_memcpy4</STRONG> (Thumb, 0 bytes, Stack size 8 bytes, rt_memcpy_w.o(.text))
<BR><BR>[Stack]<UL><LI>Max Depth = 8<LI>Call Chain = __aeabi_memcpy4
</UL>
<BR>[Called By]<UL><LI><a href="#[bf]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;DDSDataInit
<LI><a href="#[a3]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;AD9959_senddata
</UL>

<P><STRONG><a name="[195]"></a>__aeabi_memcpy8</STRONG> (Thumb, 0 bytes, Stack size 8 bytes, rt_memcpy_w.o(.text), UNUSED)

<P><STRONG><a name="[196]"></a>__rt_memcpy_w</STRONG> (Thumb, 100 bytes, Stack size 8 bytes, rt_memcpy_w.o(.text), UNUSED)

<P><STRONG><a name="[197]"></a>_memcpy_lastbytes_aligned</STRONG> (Thumb, 0 bytes, Stack size unknown bytes, rt_memcpy_w.o(.text), UNUSED)

<P><STRONG><a name="[86]"></a>__aeabi_memclr4</STRONG> (Thumb, 0 bytes, Stack size 4 bytes, rt_memclr_w.o(.text))
<BR><BR>[Stack]<UL><LI>Max Depth = 4<LI>Call Chain = __aeabi_memclr4
</UL>
<BR>[Called By]<UL><LI><a href="#[111]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;OS_Num_Show
<LI><a href="#[85]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;arm_biquad_cascade_df1_init_f32
</UL>

<P><STRONG><a name="[198]"></a>__aeabi_memclr8</STRONG> (Thumb, 0 bytes, Stack size 4 bytes, rt_memclr_w.o(.text), UNUSED)

<P><STRONG><a name="[199]"></a>__rt_memclr_w</STRONG> (Thumb, 78 bytes, Stack size 4 bytes, rt_memclr_w.o(.text), UNUSED)

<P><STRONG><a name="[19a]"></a>_memset_w</STRONG> (Thumb, 0 bytes, Stack size unknown bytes, rt_memclr_w.o(.text), UNUSED)

<P><STRONG><a name="[19b]"></a>__use_two_region_memory</STRONG> (Thumb, 2 bytes, Stack size 0 bytes, heapauxi.o(.text), UNUSED)

<P><STRONG><a name="[19c]"></a>__rt_heap_escrow$2region</STRONG> (Thumb, 2 bytes, Stack size 0 bytes, heapauxi.o(.text), UNUSED)

<P><STRONG><a name="[19d]"></a>__rt_heap_expand$2region</STRONG> (Thumb, 2 bytes, Stack size 0 bytes, heapauxi.o(.text), UNUSED)

<P><STRONG><a name="[19e]"></a>__I$use$semihosting</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, use_no_semi.o(.text), UNUSED)

<P><STRONG><a name="[19f]"></a>__use_no_semihosting_swi</STRONG> (Thumb, 2 bytes, Stack size 0 bytes, use_no_semi.o(.text), UNUSED)

<P><STRONG><a name="[8c]"></a>__read_errno</STRONG> (Thumb, 10 bytes, Stack size 8 bytes, _rserrno.o(.text), UNUSED)
<BR><BR>[Calls]<UL><LI><a href="#[8d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_errno_addr
</UL>

<P><STRONG><a name="[8e]"></a>__set_errno</STRONG> (Thumb, 12 bytes, Stack size 8 bytes, _rserrno.o(.text))
<BR><BR>[Stack]<UL><LI>Max Depth = 8<LI>Call Chain = __set_errno
</UL>
<BR>[Calls]<UL><LI><a href="#[8d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_errno_addr
</UL>
<BR>[Called By]<UL><LI><a href="#[129]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__hardfp_pow
<LI><a href="#[14f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;sqrt
<LI><a href="#[f8]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__hardfp_sqrt
</UL>

<P><STRONG><a name="[1a0]"></a>__lib_sel_fp_printf</STRONG> (Thumb, 2 bytes, Stack size 0 bytes, _printf_fp_dec.o(.text), UNUSED)

<P><STRONG><a name="[95]"></a>_printf_fp_dec_real</STRONG> (Thumb, 620 bytes, Stack size 104 bytes, _printf_fp_dec.o(.text))
<BR><BR>[Stack]<UL><LI>Max Depth = 324<LI>Call Chain = _printf_fp_dec_real &rArr; _fp_digits &rArr; _btod_etento &rArr; _btod_emul &rArr; _e2e
</UL>
<BR>[Calls]<UL><LI><a href="#[99]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_printf_post_padding
<LI><a href="#[98]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_printf_pre_padding
<LI><a href="#[97]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_printf_fp_infnan
<LI><a href="#[77]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__rt_locale
<LI><a href="#[96]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__ARM_fpclassify
<LI><a href="#[8f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_fp_digits
</UL>
<BR>[Called By]<UL><LI><a href="#[6f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_printf_fp_dec
</UL>

<P><STRONG><a name="[9a]"></a>_printf_fp_hex_real</STRONG> (Thumb, 756 bytes, Stack size 72 bytes, _printf_fp_hex.o(.text))
<BR><BR>[Stack]<UL><LI>Max Depth = 112<LI>Call Chain = _printf_fp_hex_real &rArr; _printf_fp_infnan &rArr; _printf_post_padding
</UL>
<BR>[Calls]<UL><LI><a href="#[99]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_printf_post_padding
<LI><a href="#[98]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_printf_pre_padding
<LI><a href="#[97]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_printf_fp_infnan
<LI><a href="#[96]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__ARM_fpclassify
</UL>
<BR>[Called By]<UL><LI><a href="#[73]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_printf_fp_hex
</UL>

<P><STRONG><a name="[88]"></a>_printf_char_common</STRONG> (Thumb, 32 bytes, Stack size 64 bytes, _printf_char_common.o(.text))
<BR><BR>[Stack]<UL><LI>Max Depth = 104 + Unknown Stack Size
<LI>Call Chain = _printf_char_common &rArr; __printf
</UL>
<BR>[Calls]<UL><LI><a href="#[89]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__printf
</UL>
<BR>[Called By]<UL><LI><a href="#[87]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__2sprintf
</UL>

<P><STRONG><a name="[62]"></a>_sputc</STRONG> (Thumb, 10 bytes, Stack size 0 bytes, _sputc.o(.text))
<BR><BR>[Called By]<UL><LI><a href="#[87]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__2sprintf
</UL>
<BR>[Address Reference Count : 1]<UL><LI> noretval__2sprintf.o(.text)
</UL>
<P><STRONG><a name="[77]"></a>__rt_locale</STRONG> (Thumb, 8 bytes, Stack size 0 bytes, rt_locale_intlibspace.o(.text))
<BR><BR>[Called By]<UL><LI><a href="#[76]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__rt_lib_init_lc_common
<LI><a href="#[95]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_printf_fp_dec_real
</UL>

<P><STRONG><a name="[8d]"></a>__aeabi_errno_addr</STRONG> (Thumb, 8 bytes, Stack size 0 bytes, rt_errno_addr_intlibspace.o(.text))
<BR><BR>[Called By]<UL><LI><a href="#[8e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__set_errno
<LI><a href="#[8c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__read_errno
</UL>

<P><STRONG><a name="[1a1]"></a>__errno$intlibspace</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, rt_errno_addr_intlibspace.o(.text), UNUSED)

<P><STRONG><a name="[1a2]"></a>__rt_errno_addr$intlibspace</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, rt_errno_addr_intlibspace.o(.text), UNUSED)

<P><STRONG><a name="[94]"></a>_ll_udiv10</STRONG> (Thumb, 138 bytes, Stack size 12 bytes, lludiv10.o(.text))
<BR><BR>[Stack]<UL><LI>Max Depth = 12<LI>Call Chain = _ll_udiv10
</UL>
<BR>[Called By]<UL><LI><a href="#[8f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_fp_digits
</UL>

<P><STRONG><a name="[97]"></a>_printf_fp_infnan</STRONG> (Thumb, 112 bytes, Stack size 24 bytes, _printf_fp_infnan.o(.text))
<BR><BR>[Stack]<UL><LI>Max Depth = 40<LI>Call Chain = _printf_fp_infnan &rArr; _printf_post_padding
</UL>
<BR>[Calls]<UL><LI><a href="#[99]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_printf_post_padding
<LI><a href="#[98]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_printf_pre_padding
</UL>
<BR>[Called By]<UL><LI><a href="#[9a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_printf_fp_hex_real
<LI><a href="#[95]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_printf_fp_dec_real
</UL>

<P><STRONG><a name="[90]"></a>_btod_etento</STRONG> (Thumb, 224 bytes, Stack size 72 bytes, bigflt0.o(.text))
<BR><BR>[Stack]<UL><LI>Max Depth = 124<LI>Call Chain = _btod_etento &rArr; _btod_emul &rArr; _e2e
</UL>
<BR>[Calls]<UL><LI><a href="#[93]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_btod_emul
<LI><a href="#[92]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_btod_ediv
</UL>
<BR>[Called By]<UL><LI><a href="#[8f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_fp_digits
</UL>

<P><STRONG><a name="[1a3]"></a>__user_libspace</STRONG> (Thumb, 8 bytes, Stack size 0 bytes, libspace.o(.text), UNUSED)

<P><STRONG><a name="[9b]"></a>__user_perproc_libspace</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, libspace.o(.text))
<BR><BR>[Called By]<UL><LI><a href="#[7b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__user_setup_stackheap
</UL>

<P><STRONG><a name="[1a4]"></a>__user_perthread_libspace</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, libspace.o(.text), UNUSED)

<P><STRONG><a name="[7b]"></a>__user_setup_stackheap</STRONG> (Thumb, 74 bytes, Stack size 8 bytes, sys_stackheap_outer.o(.text))
<BR><BR>[Stack]<UL><LI>Max Depth = 8 + Unknown Stack Size
<LI>Call Chain = __user_setup_stackheap
</UL>
<BR>[Calls]<UL><LI><a href="#[9c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__user_initial_stackheap
<LI><a href="#[9b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__user_perproc_libspace
</UL>
<BR>[Called By]<UL><LI><a href="#[7a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__rt_entry_sh
</UL>

<P><STRONG><a name="[80]"></a>exit</STRONG> (Thumb, 18 bytes, Stack size 8 bytes, exit.o(.text))
<BR><BR>[Stack]<UL><LI>Max Depth = 8 + Unknown Stack Size
<LI>Call Chain = exit
</UL>
<BR>[Calls]<UL><LI><a href="#[9d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__rt_exit
</UL>
<BR>[Called By]<UL><LI><a href="#[7e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__rt_entry_main
</UL>

<P><STRONG><a name="[15b]"></a>strcmp</STRONG> (Thumb, 128 bytes, Stack size 0 bytes, strcmpv7m.o(.text))
<BR><BR>[Called By]<UL><LI><a href="#[79]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_get_lc_numeric
</UL>

<P><STRONG><a name="[91]"></a>_btod_d2e</STRONG> (Thumb, 62 bytes, Stack size 0 bytes, btod.o(CL$$btod_d2e))
<BR><BR>[Calls]<UL><LI><a href="#[9e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_d2e_norm_op1
</UL>
<BR>[Called By]<UL><LI><a href="#[8f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_fp_digits
</UL>

<P><STRONG><a name="[9f]"></a>_d2e_denorm_low</STRONG> (Thumb, 70 bytes, Stack size 0 bytes, btod.o(CL$$btod_d2e_denorm_low))
<BR><BR>[Called By]<UL><LI><a href="#[9e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_d2e_norm_op1
</UL>

<P><STRONG><a name="[9e]"></a>_d2e_norm_op1</STRONG> (Thumb, 96 bytes, Stack size 0 bytes, btod.o(CL$$btod_d2e_norm_op1))
<BR><BR>[Calls]<UL><LI><a href="#[9f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_d2e_denorm_low
</UL>
<BR>[Called By]<UL><LI><a href="#[91]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_btod_d2e
</UL>

<P><STRONG><a name="[a0]"></a>__btod_div_common</STRONG> (Thumb, 696 bytes, Stack size 24 bytes, btod.o(CL$$btod_div_common))
<BR><BR>[Stack]<UL><LI>Max Depth = 24<LI>Call Chain = __btod_div_common
</UL>
<BR>[Called By]<UL><LI><a href="#[92]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_btod_ediv
</UL>

<P><STRONG><a name="[a1]"></a>_e2e</STRONG> (Thumb, 220 bytes, Stack size 24 bytes, btod.o(CL$$btod_e2e))
<BR><BR>[Stack]<UL><LI>Max Depth = 24<LI>Call Chain = _e2e
</UL>
<BR>[Called By]<UL><LI><a href="#[93]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_btod_emul
<LI><a href="#[92]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_btod_ediv
</UL>

<P><STRONG><a name="[92]"></a>_btod_ediv</STRONG> (Thumb, 42 bytes, Stack size 28 bytes, btod.o(CL$$btod_ediv))
<BR><BR>[Stack]<UL><LI>Max Depth = 52<LI>Call Chain = _btod_ediv &rArr; _e2e
</UL>
<BR>[Calls]<UL><LI><a href="#[a1]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_e2e
<LI><a href="#[a0]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__btod_div_common
</UL>
<BR>[Called By]<UL><LI><a href="#[90]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_btod_etento
<LI><a href="#[8f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_fp_digits
</UL>

<P><STRONG><a name="[93]"></a>_btod_emul</STRONG> (Thumb, 42 bytes, Stack size 28 bytes, btod.o(CL$$btod_emul))
<BR><BR>[Stack]<UL><LI>Max Depth = 52<LI>Call Chain = _btod_emul &rArr; _e2e
</UL>
<BR>[Calls]<UL><LI><a href="#[a2]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__btod_mult_common
<LI><a href="#[a1]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_e2e
</UL>
<BR>[Called By]<UL><LI><a href="#[90]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_btod_etento
<LI><a href="#[8f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_fp_digits
</UL>

<P><STRONG><a name="[a2]"></a>__btod_mult_common</STRONG> (Thumb, 580 bytes, Stack size 16 bytes, btod.o(CL$$btod_mult_common))
<BR><BR>[Stack]<UL><LI>Max Depth = 16<LI>Call Chain = __btod_mult_common
</UL>
<BR>[Called By]<UL><LI><a href="#[93]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_btod_emul
</UL>

<P><STRONG><a name="[101]"></a>OS_CPU_SR_Save</STRONG> (Thumb, 0 bytes, Stack size unknown bytes, core.o(CODE))
<BR><BR>[Called By]<UL><LI><a href="#[ef]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;OSTimeDly
<LI><a href="#[d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SysTick_Handler
<LI><a href="#[116]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;OS_SchedUnlock
<LI><a href="#[115]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;OS_SchedLock
<LI><a href="#[104]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;OS_Sched
</UL>

<P><STRONG><a name="[103]"></a>OS_CPU_SR_Restore</STRONG> (Thumb, 0 bytes, Stack size unknown bytes, core.o(CODE))
<BR><BR>[Called By]<UL><LI><a href="#[ef]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;OSTimeDly
<LI><a href="#[d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SysTick_Handler
<LI><a href="#[116]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;OS_SchedUnlock
<LI><a href="#[115]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;OS_SchedLock
<LI><a href="#[104]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;OS_Sched
</UL>

<P><STRONG><a name="[114]"></a>OSCtxSw</STRONG> (Thumb, 0 bytes, Stack size unknown bytes, core.o(CODE))
<BR><BR>[Called By]<UL><LI><a href="#[104]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;OS_Sched
</UL>

<P><STRONG><a name="[119]"></a>OSStartHighRdy</STRONG> (Thumb, 0 bytes, Stack size unknown bytes, core.o(CODE))
<BR><BR>[Called By]<UL><LI><a href="#[117]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;OS_Start
</UL>

<P><STRONG><a name="[c]"></a>PendSV_Handler</STRONG> (Thumb, 0 bytes, Stack size unknown bytes, core.o(CODE))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f40_41xxx.o(RESET)
</UL>
<P><STRONG><a name="[a3]"></a>AD9959_senddata</STRONG> (Thumb, 138 bytes, Stack size 224 bytes, user.o(i.AD9959_senddata))
<BR><BR>[Stack]<UL><LI>Max Depth = 280 + Unknown Stack Size
<LI>Call Chain = AD9959_senddata &rArr; sendData &rArr; usartSendData
</UL>
<BR>[Calls]<UL><LI><a href="#[a5]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;sendData
<LI><a href="#[a6]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;delay_ms
<LI><a href="#[a4]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_memcpy4
</UL>
<BR>[Called By]<UL><LI><a href="#[f0]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;MenuHaddler_1
</UL>

<P><STRONG><a name="[b1]"></a>ADC_Cmd</STRONG> (Thumb, 22 bytes, Stack size 0 bytes, stm32f4xx_adc.o(i.ADC_Cmd))
<BR><BR>[Called By]<UL><LI><a href="#[b2]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ADC_Mode_Independent_Init
<LI><a href="#[aa]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ADC_DualMode_RegSimult_Init
</UL>

<P><STRONG><a name="[ac]"></a>ADC_CommonInit</STRONG> (Thumb, 34 bytes, Stack size 0 bytes, stm32f4xx_adc.o(i.ADC_CommonInit))
<BR><BR>[Called By]<UL><LI><a href="#[b2]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ADC_Mode_Independent_Init
<LI><a href="#[aa]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ADC_DualMode_RegSimult_Init
</UL>

<P><STRONG><a name="[b0]"></a>ADC_DMACmd</STRONG> (Thumb, 22 bytes, Stack size 0 bytes, stm32f4xx_adc.o(i.ADC_DMACmd))
<BR><BR>[Called By]<UL><LI><a href="#[b2]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ADC_Mode_Independent_Init
<LI><a href="#[aa]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ADC_DualMode_RegSimult_Init
</UL>

<P><STRONG><a name="[af]"></a>ADC_DMARequestAfterLastTransferCmd</STRONG> (Thumb, 22 bytes, Stack size 0 bytes, stm32f4xx_adc.o(i.ADC_DMARequestAfterLastTransferCmd))
<BR><BR>[Called By]<UL><LI><a href="#[b2]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ADC_Mode_Independent_Init
<LI><a href="#[aa]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ADC_DualMode_RegSimult_Init
</UL>

<P><STRONG><a name="[a7]"></a>ADC_DMA_NVIC_Init</STRONG> (Thumb, 42 bytes, Stack size 8 bytes, user_adc.o(i.ADC_DMA_NVIC_Init))
<BR><BR>[Stack]<UL><LI>Max Depth = 24<LI>Call Chain = ADC_DMA_NVIC_Init &rArr; NVIC_Init
</UL>
<BR>[Calls]<UL><LI><a href="#[a8]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;NVIC_PriorityGroupConfig
<LI><a href="#[a9]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;NVIC_Init
</UL>
<BR>[Called By]<UL><LI><a href="#[ce]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;User_ADC_Init
</UL>

<P><STRONG><a name="[aa]"></a>ADC_DualMode_RegSimult_Init</STRONG> (Thumb, 172 bytes, Stack size 48 bytes, user_adc.o(i.ADC_DualMode_RegSimult_Init))
<BR><BR>[Stack]<UL><LI>Max Depth = 68<LI>Call Chain = ADC_DualMode_RegSimult_Init &rArr; ADC_RegularChannelConfig
</UL>
<BR>[Calls]<UL><LI><a href="#[ab]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;RCC_APB2PeriphClockCmd
<LI><a href="#[ae]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ADC_RegularChannelConfig
<LI><a href="#[ad]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ADC_Init
<LI><a href="#[af]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ADC_DMARequestAfterLastTransferCmd
<LI><a href="#[b0]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ADC_DMACmd
<LI><a href="#[ac]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ADC_CommonInit
<LI><a href="#[b1]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ADC_Cmd
</UL>
<BR>[Called By]<UL><LI><a href="#[ce]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;User_ADC_Init
</UL>

<P><STRONG><a name="[ad]"></a>ADC_Init</STRONG> (Thumb, 74 bytes, Stack size 12 bytes, stm32f4xx_adc.o(i.ADC_Init))
<BR><BR>[Stack]<UL><LI>Max Depth = 12<LI>Call Chain = ADC_Init
</UL>
<BR>[Called By]<UL><LI><a href="#[b2]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ADC_Mode_Independent_Init
<LI><a href="#[aa]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ADC_DualMode_RegSimult_Init
</UL>

<P><STRONG><a name="[b2]"></a>ADC_Mode_Independent_Init</STRONG> (Thumb, 116 bytes, Stack size 48 bytes, user_adc.o(i.ADC_Mode_Independent_Init))
<BR><BR>[Stack]<UL><LI>Max Depth = 68<LI>Call Chain = ADC_Mode_Independent_Init &rArr; ADC_RegularChannelConfig
</UL>
<BR>[Calls]<UL><LI><a href="#[ab]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;RCC_APB2PeriphClockCmd
<LI><a href="#[ae]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ADC_RegularChannelConfig
<LI><a href="#[ad]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ADC_Init
<LI><a href="#[af]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ADC_DMARequestAfterLastTransferCmd
<LI><a href="#[b0]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ADC_DMACmd
<LI><a href="#[ac]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ADC_CommonInit
<LI><a href="#[b1]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ADC_Cmd
</UL>
<BR>[Called By]<UL><LI><a href="#[ce]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;User_ADC_Init
</UL>

<P><STRONG><a name="[ae]"></a>ADC_RegularChannelConfig</STRONG> (Thumb, 184 bytes, Stack size 20 bytes, stm32f4xx_adc.o(i.ADC_RegularChannelConfig))
<BR><BR>[Stack]<UL><LI>Max Depth = 20<LI>Call Chain = ADC_RegularChannelConfig
</UL>
<BR>[Called By]<UL><LI><a href="#[b2]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ADC_Mode_Independent_Init
<LI><a href="#[aa]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ADC_DualMode_RegSimult_Init
</UL>

<P><STRONG><a name="[b3]"></a>ADC_TIM3_Init</STRONG> (Thumb, 96 bytes, Stack size 24 bytes, user_adc.o(i.ADC_TIM3_Init))
<BR><BR>[Stack]<UL><LI>Max Depth = 48<LI>Call Chain = ADC_TIM3_Init &rArr; Set_SamplingFre
</UL>
<BR>[Calls]<UL><LI><a href="#[b7]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;TIM_UpdateDisableConfig
<LI><a href="#[b5]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;TIM_TimeBaseInit
<LI><a href="#[b6]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;TIM_SelectOutputTrigger
<LI><a href="#[b8]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;TIM_Cmd
<LI><a href="#[b4]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;RCC_APB1PeriphClockCmd
<LI><a href="#[b9]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Set_SamplingFre
</UL>
<BR>[Called By]<UL><LI><a href="#[ce]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;User_ADC_Init
</UL>

<P><STRONG><a name="[8]"></a>BusFault_Handler</STRONG> (Thumb, 4 bytes, Stack size 0 bytes, stm32f4xx_it.o(i.BusFault_Handler))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f40_41xxx.o(RESET)
</UL>
<P><STRONG><a name="[ba]"></a>Change_Menu</STRONG> (Thumb, 168 bytes, Stack size 16 bytes, user.o(i.Change_Menu))
<BR><BR>[Stack]<UL><LI>Max Depth = 328<LI>Call Chain = Change_Menu &rArr; OS_String_Show &rArr; OS_Font_Show &rArr; OS_HzMat_Get &rArr; W25Q64_Read &rArr; SPI1_ReadWriteByte
</UL>
<BR>[Calls]<UL><LI><a href="#[bc]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;OS_String_Show
<LI><a href="#[bb]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;LCD_Appoint_Clear
</UL>
<BR>[Called By]<UL><LI><a href="#[66]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;User_main
<LI><a href="#[ff]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;MenuHaddler_4
<LI><a href="#[fd]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;MenuHaddler_3
<LI><a href="#[fc]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;MenuHaddler_2
<LI><a href="#[f0]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;MenuHaddler_1
</UL>

<P><STRONG><a name="[142]"></a>DAC_Cmd</STRONG> (Thumb, 34 bytes, Stack size 0 bytes, stm32f4xx_dac.o(i.DAC_Cmd))
<BR><BR>[Called By]<UL><LI><a href="#[13e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;User_DAC_Configure
</UL>

<P><STRONG><a name="[141]"></a>DAC_DMACmd</STRONG> (Thumb, 38 bytes, Stack size 0 bytes, stm32f4xx_dac.o(i.DAC_DMACmd))
<BR><BR>[Called By]<UL><LI><a href="#[13e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;User_DAC_Configure
</UL>

<P><STRONG><a name="[bd]"></a>DAC_DeInit</STRONG> (Thumb, 22 bytes, Stack size 8 bytes, stm32f4xx_dac.o(i.DAC_DeInit))
<BR><BR>[Stack]<UL><LI>Max Depth = 8<LI>Call Chain = DAC_DeInit
</UL>
<BR>[Calls]<UL><LI><a href="#[be]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;RCC_APB1PeriphResetCmd
</UL>
<BR>[Called By]<UL><LI><a href="#[13e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;User_DAC_Configure
</UL>

<P><STRONG><a name="[140]"></a>DAC_Init</STRONG> (Thumb, 46 bytes, Stack size 12 bytes, stm32f4xx_dac.o(i.DAC_Init))
<BR><BR>[Stack]<UL><LI>Max Depth = 12<LI>Call Chain = DAC_Init
</UL>
<BR>[Called By]<UL><LI><a href="#[13e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;User_DAC_Configure
</UL>

<P><STRONG><a name="[f2]"></a>DAC_SetChannel1Data</STRONG> (Thumb, 26 bytes, Stack size 8 bytes, stm32f4xx_dac.o(i.DAC_SetChannel1Data))
<BR><BR>[Stack]<UL><LI>Max Depth = 8<LI>Call Chain = DAC_SetChannel1Data
</UL>
<BR>[Called By]<UL><LI><a href="#[d5]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;User_DAC_Init
<LI><a href="#[f0]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;MenuHaddler_1
</UL>

<P><STRONG><a name="[13f]"></a>DAC_StructInit</STRONG> (Thumb, 12 bytes, Stack size 0 bytes, stm32f4xx_dac.o(i.DAC_StructInit))
<BR><BR>[Called By]<UL><LI><a href="#[13e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;User_DAC_Configure
</UL>

<P><STRONG><a name="[bf]"></a>DDSDataInit</STRONG> (Thumb, 350 bytes, Stack size 224 bytes, drive_communication.o(i.DDSDataInit))
<BR><BR>[Stack]<UL><LI>Max Depth = 280 + Unknown Stack Size
<LI>Call Chain = DDSDataInit &rArr; sendData &rArr; usartSendData
</UL>
<BR>[Calls]<UL><LI><a href="#[a5]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;sendData
<LI><a href="#[a6]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;delay_ms
<LI><a href="#[a4]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_memcpy4
</UL>
<BR>[Called By]<UL><LI><a href="#[d3]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Init_All
</UL>

<P><STRONG><a name="[46]"></a>DMA2_Stream0_IRQHandler</STRONG> (Thumb, 338 bytes, Stack size 24 bytes, user_adc.o(i.DMA2_Stream0_IRQHandler))
<BR><BR>[Stack]<UL><LI>Max Depth = 40<LI>Call Chain = DMA2_Stream0_IRQHandler &rArr; arm_biquad_cascade_df1_f32
</UL>
<BR>[Calls]<UL><LI><a href="#[c1]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;arm_biquad_cascade_df1_f32
<LI><a href="#[b8]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;TIM_Cmd
<LI><a href="#[c0]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;DMA_ClearFlag
</UL>
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f40_41xxx.o(RESET)
</UL>
<P><STRONG><a name="[c0]"></a>DMA_ClearFlag</STRONG> (Thumb, 38 bytes, Stack size 0 bytes, stm32f4xx_dma.o(i.DMA_ClearFlag))
<BR><BR>[Called By]<UL><LI><a href="#[c8]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;IIR_ADC_DMA_Init
<LI><a href="#[46]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;DMA2_Stream0_IRQHandler
</UL>

<P><STRONG><a name="[cc]"></a>DMA_Cmd</STRONG> (Thumb, 22 bytes, Stack size 0 bytes, stm32f4xx_dma.o(i.DMA_Cmd))
<BR><BR>[Called By]<UL><LI><a href="#[c8]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;IIR_ADC_DMA_Init
<LI><a href="#[143]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;User_DAC_DMA_Init
</UL>

<P><STRONG><a name="[c9]"></a>DMA_DeInit</STRONG> (Thumb, 324 bytes, Stack size 0 bytes, stm32f4xx_dma.o(i.DMA_DeInit))
<BR><BR>[Called By]<UL><LI><a href="#[c8]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;IIR_ADC_DMA_Init
<LI><a href="#[143]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;User_DAC_DMA_Init
</UL>

<P><STRONG><a name="[cb]"></a>DMA_ITConfig</STRONG> (Thumb, 58 bytes, Stack size 8 bytes, stm32f4xx_dma.o(i.DMA_ITConfig))
<BR><BR>[Stack]<UL><LI>Max Depth = 8<LI>Call Chain = DMA_ITConfig
</UL>
<BR>[Called By]<UL><LI><a href="#[c8]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;IIR_ADC_DMA_Init
</UL>

<P><STRONG><a name="[ca]"></a>DMA_Init</STRONG> (Thumb, 82 bytes, Stack size 8 bytes, stm32f4xx_dma.o(i.DMA_Init))
<BR><BR>[Stack]<UL><LI>Max Depth = 8<LI>Call Chain = DMA_Init
</UL>
<BR>[Called By]<UL><LI><a href="#[c8]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;IIR_ADC_DMA_Init
<LI><a href="#[143]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;User_DAC_DMA_Init
</UL>

<P><STRONG><a name="[b]"></a>DebugMon_Handler</STRONG> (Thumb, 2 bytes, Stack size 0 bytes, stm32f4xx_it.o(i.DebugMon_Handler))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f40_41xxx.o(RESET)
</UL>
<P><STRONG><a name="[15]"></a>EXTI1_IRQHandler</STRONG> (Thumb, 28 bytes, Stack size 8 bytes, user_adc.o(i.EXTI1_IRQHandler))
<BR><BR>[Stack]<UL><LI>Max Depth = 16<LI>Call Chain = EXTI1_IRQHandler &rArr; Stop_ADC_Sampling
</UL>
<BR>[Calls]<UL><LI><a href="#[c2]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;EXTI_GetITStatus
<LI><a href="#[c4]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;EXTI_ClearITPendingBit
<LI><a href="#[c3]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Stop_ADC_Sampling
</UL>
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f40_41xxx.o(RESET)
</UL>
<P><STRONG><a name="[c4]"></a>EXTI_ClearITPendingBit</STRONG> (Thumb, 6 bytes, Stack size 0 bytes, stm32f4xx_exti.o(i.EXTI_ClearITPendingBit))
<BR><BR>[Called By]<UL><LI><a href="#[15]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;EXTI1_IRQHandler
</UL>

<P><STRONG><a name="[c2]"></a>EXTI_GetITStatus</STRONG> (Thumb, 20 bytes, Stack size 0 bytes, stm32f4xx_exti.o(i.EXTI_GetITStatus))
<BR><BR>[Called By]<UL><LI><a href="#[15]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;EXTI1_IRQHandler
</UL>

<P><STRONG><a name="[11c]"></a>EXTI_Init</STRONG> (Thumb, 142 bytes, Stack size 0 bytes, stm32f4xx_exti.o(i.EXTI_Init))
<BR><BR>[Called By]<UL><LI><a href="#[11a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;PC1_EXTI_Config
</UL>

<P><STRONG><a name="[ea]"></a>FSMC_NORSRAMCmd</STRONG> (Thumb, 46 bytes, Stack size 0 bytes, stm32f4xx_fsmc.o(i.FSMC_NORSRAMCmd))
<BR><BR>[Called By]<UL><LI><a href="#[e7]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;LCD_FSMCConfig
</UL>

<P><STRONG><a name="[e9]"></a>FSMC_NORSRAMInit</STRONG> (Thumb, 230 bytes, Stack size 0 bytes, stm32f4xx_fsmc.o(i.FSMC_NORSRAMInit))
<BR><BR>[Called By]<UL><LI><a href="#[e7]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;LCD_FSMCConfig
</UL>

<P><STRONG><a name="[c5]"></a>GPIO_Config</STRONG> (Thumb, 46 bytes, Stack size 16 bytes, user.o(i.GPIO_Config))
<BR><BR>[Stack]<UL><LI>Max Depth = 36<LI>Call Chain = GPIO_Config &rArr; GPIO_Init
</UL>
<BR>[Calls]<UL><LI><a href="#[c6]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;RCC_AHB1PeriphClockCmd
<LI><a href="#[c7]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;GPIO_Init
</UL>
<BR>[Called By]<UL><LI><a href="#[d3]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Init_All
</UL>

<P><STRONG><a name="[c7]"></a>GPIO_Init</STRONG> (Thumb, 144 bytes, Stack size 20 bytes, stm32f4xx_gpio.o(i.GPIO_Init))
<BR><BR>[Stack]<UL><LI>Max Depth = 20<LI>Call Chain = GPIO_Init
</UL>
<BR>[Called By]<UL><LI><a href="#[ed]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;LED_Init
<LI><a href="#[d7]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Init_Uart
<LI><a href="#[c5]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;GPIO_Config
<LI><a href="#[13d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;User_ADC_GPIO_Init
<LI><a href="#[11d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;PC1_GPIO_Config
<LI><a href="#[144]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;User_DAC_GPIO_Init
<LI><a href="#[125]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;PS2_SCL_Set
<LI><a href="#[124]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;PS2_GPIO_Init
<LI><a href="#[11f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SPI_GPIO_Init
<LI><a href="#[e0]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;LCD_CtrlLinesConfig
<LI><a href="#[12c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SPI1_Init
<LI><a href="#[149]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;W25Q64_Init
</UL>

<P><STRONG><a name="[d9]"></a>GPIO_PinAFConfig</STRONG> (Thumb, 70 bytes, Stack size 20 bytes, stm32f4xx_gpio.o(i.GPIO_PinAFConfig))
<BR><BR>[Stack]<UL><LI>Max Depth = 20<LI>Call Chain = GPIO_PinAFConfig
</UL>
<BR>[Called By]<UL><LI><a href="#[d7]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Init_Uart
<LI><a href="#[11f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SPI_GPIO_Init
<LI><a href="#[e0]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;LCD_CtrlLinesConfig
<LI><a href="#[12c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SPI1_Init
</UL>

<P><STRONG><a name="[127]"></a>GPIO_ReadInputDataBit</STRONG> (Thumb, 18 bytes, Stack size 0 bytes, stm32f4xx_gpio.o(i.GPIO_ReadInputDataBit))
<BR><BR>[Called By]<UL><LI><a href="#[dd]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;PS2_ReadKeyCodon
<LI><a href="#[126]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;PS2_SCL_Wait
</UL>

<P><STRONG><a name="[12b]"></a>GPIO_ResetBits</STRONG> (Thumb, 4 bytes, Stack size 0 bytes, stm32f4xx_gpio.o(i.GPIO_ResetBits))
<BR><BR>[Called By]<UL><LI><a href="#[109]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;W25Q64_Read
<LI><a href="#[125]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;PS2_SCL_Set
<LI><a href="#[123]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;User_SPI_SendData
<LI><a href="#[14a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;W25Q64_ReadID
</UL>

<P><STRONG><a name="[12a]"></a>GPIO_SetBits</STRONG> (Thumb, 4 bytes, Stack size 0 bytes, stm32f4xx_gpio.o(i.GPIO_SetBits))
<BR><BR>[Called By]<UL><LI><a href="#[109]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;W25Q64_Read
<LI><a href="#[125]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;PS2_SCL_Set
<LI><a href="#[123]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;User_SPI_SendData
<LI><a href="#[14a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;W25Q64_ReadID
<LI><a href="#[149]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;W25Q64_Init
</UL>

<P><STRONG><a name="[6]"></a>HardFault_Handler</STRONG> (Thumb, 4 bytes, Stack size 0 bytes, stm32f4xx_it.o(i.HardFault_Handler))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f40_41xxx.o(RESET)
</UL>
<P><STRONG><a name="[c8]"></a>IIR_ADC_DMA_Init</STRONG> (Thumb, 126 bytes, Stack size 72 bytes, user_adc.o(i.IIR_ADC_DMA_Init))
<BR><BR>[Stack]<UL><LI>Max Depth = 80<LI>Call Chain = IIR_ADC_DMA_Init &rArr; DMA_Init
</UL>
<BR>[Calls]<UL><LI><a href="#[c6]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;RCC_AHB1PeriphClockCmd
<LI><a href="#[ca]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;DMA_Init
<LI><a href="#[cb]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;DMA_ITConfig
<LI><a href="#[c9]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;DMA_DeInit
<LI><a href="#[cc]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;DMA_Cmd
<LI><a href="#[c0]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;DMA_ClearFlag
</UL>
<BR>[Called By]<UL><LI><a href="#[ce]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;User_ADC_Init
</UL>

<P><STRONG><a name="[d0]"></a>IIR_Filter_Config</STRONG> (Thumb, 36 bytes, Stack size 0 bytes, user_adc.o(i.IIR_Filter_Config))
<BR><BR>[Called By]<UL><LI><a href="#[cd]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;IIR_Init
</UL>

<P><STRONG><a name="[d2]"></a>IIR_Filter_Enable</STRONG> (Thumb, 6 bytes, Stack size 0 bytes, user_adc.o(i.IIR_Filter_Enable))
<BR><BR>[Called By]<UL><LI><a href="#[fd]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;MenuHaddler_3
<LI><a href="#[cd]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;IIR_Init
</UL>

<P><STRONG><a name="[cf]"></a>IIR_Filter_Reset</STRONG> (Thumb, 48 bytes, Stack size 0 bytes, user_adc.o(i.IIR_Filter_Reset))
<BR><BR>[Called By]<UL><LI><a href="#[cd]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;IIR_Init
</UL>

<P><STRONG><a name="[cd]"></a>IIR_Init</STRONG> (Thumb, 48 bytes, Stack size 8 bytes, user_adc.o(i.IIR_Init))
<BR><BR>[Stack]<UL><LI>Max Depth = 96<LI>Call Chain = IIR_Init &rArr; User_ADC_Init &rArr; IIR_ADC_DMA_Init &rArr; DMA_Init
</UL>
<BR>[Calls]<UL><LI><a href="#[d1]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;init_iir_filter
<LI><a href="#[ce]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;User_ADC_Init
<LI><a href="#[cf]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;IIR_Filter_Reset
<LI><a href="#[d2]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;IIR_Filter_Enable
<LI><a href="#[d0]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;IIR_Filter_Config
</UL>
<BR>[Called By]<UL><LI><a href="#[d3]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Init_All
</UL>

<P><STRONG><a name="[d3]"></a>Init_All</STRONG> (Thumb, 48 bytes, Stack size 8 bytes, user.o(i.Init_All))
<BR><BR>[Stack]<UL><LI>Max Depth = 288 + Unknown Stack Size
<LI>Call Chain = Init_All &rArr; DDSDataInit &rArr; sendData &rArr; usartSendData
</UL>
<BR>[Calls]<UL><LI><a href="#[d5]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;User_DAC_Init
<LI><a href="#[d6]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;PGA2310_Init
<LI><a href="#[d4]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;LCD_Clear
<LI><a href="#[d7]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Init_Uart
<LI><a href="#[bf]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;DDSDataInit
<LI><a href="#[c5]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;GPIO_Config
<LI><a href="#[ce]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;User_ADC_Init
<LI><a href="#[d8]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;PC1_Init
<LI><a href="#[cd]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;IIR_Init
</UL>
<BR>[Called By]<UL><LI><a href="#[66]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;User_main
</UL>

<P><STRONG><a name="[d7]"></a>Init_Uart</STRONG> (Thumb, 124 bytes, Stack size 32 bytes, drive_communication.o(i.Init_Uart))
<BR><BR>[Stack]<UL><LI>Max Depth = 100<LI>Call Chain = Init_Uart &rArr; USART_Init &rArr; RCC_GetClocksFreq
</UL>
<BR>[Calls]<UL><LI><a href="#[ab]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;RCC_APB2PeriphClockCmd
<LI><a href="#[c6]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;RCC_AHB1PeriphClockCmd
<LI><a href="#[c7]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;GPIO_Init
<LI><a href="#[da]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;USART_Init
<LI><a href="#[db]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;USART_Cmd
<LI><a href="#[d9]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;GPIO_PinAFConfig
</UL>
<BR>[Called By]<UL><LI><a href="#[d3]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Init_All
</UL>

<P><STRONG><a name="[dc]"></a>Key_StateSweep</STRONG> (Thumb, 192 bytes, Stack size 8 bytes, drive_ps2.o(i.Key_StateSweep))
<BR><BR>[Stack]<UL><LI>Max Depth = 68<LI>Call Chain = Key_StateSweep &rArr; PS2_ReadKeyCodon &rArr; PS2_SCL_Wait &rArr; PS2_SCL_Set &rArr; GPIO_Init
</UL>
<BR>[Calls]<UL><LI><a href="#[dd]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;PS2_ReadKeyCodon
</UL>
<BR>[Called By]<UL><LI><a href="#[67]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;MyPs2KeyScan
</UL>

<P><STRONG><a name="[bb]"></a>LCD_Appoint_Clear</STRONG> (Thumb, 72 bytes, Stack size 32 bytes, tft_lcd.o(i.LCD_Appoint_Clear))
<BR><BR>[Stack]<UL><LI>Max Depth = 48<LI>Call Chain = LCD_Appoint_Clear &rArr; LCD_SetCursor
</UL>
<BR>[Calls]<UL><LI><a href="#[df]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;LCD_WriteRAM_Prepare
<LI><a href="#[de]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;LCD_SetCursor
</UL>
<BR>[Called By]<UL><LI><a href="#[148]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;menu_show
<LI><a href="#[f3]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;PS2_ReadNum
<LI><a href="#[f0]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;MenuHaddler_1
<LI><a href="#[ba]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Change_Menu
</UL>

<P><STRONG><a name="[d4]"></a>LCD_Clear</STRONG> (Thumb, 38 bytes, Stack size 16 bytes, tft_lcd.o(i.LCD_Clear))
<BR><BR>[Stack]<UL><LI>Max Depth = 32<LI>Call Chain = LCD_Clear &rArr; LCD_SetCursor
</UL>
<BR>[Calls]<UL><LI><a href="#[df]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;LCD_WriteRAM_Prepare
<LI><a href="#[de]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;LCD_SetCursor
</UL>
<BR>[Called By]<UL><LI><a href="#[10d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;OS_LCD_Init
<LI><a href="#[d3]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Init_All
</UL>

<P><STRONG><a name="[e0]"></a>LCD_CtrlLinesConfig</STRONG> (Thumb, 322 bytes, Stack size 16 bytes, tft_lcd.o(i.LCD_CtrlLinesConfig))
<BR><BR>[Stack]<UL><LI>Max Depth = 36<LI>Call Chain = LCD_CtrlLinesConfig &rArr; GPIO_Init
</UL>
<BR>[Calls]<UL><LI><a href="#[c6]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;RCC_AHB1PeriphClockCmd
<LI><a href="#[c7]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;GPIO_Init
<LI><a href="#[d9]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;GPIO_PinAFConfig
</UL>
<BR>[Called By]<UL><LI><a href="#[10e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;TFT_LCD_Init
</UL>

<P><STRONG><a name="[e1]"></a>LCD_DrawLine</STRONG> (Thumb, 86 bytes, Stack size 24 bytes, tft_lcd.o(i.LCD_DrawLine))
<BR><BR>[Stack]<UL><LI>Max Depth = 40<LI>Call Chain = LCD_DrawLine &rArr; LCD_SetCursor
</UL>
<BR>[Calls]<UL><LI><a href="#[df]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;LCD_WriteRAM_Prepare
<LI><a href="#[e2]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;LCD_WriteRAM
<LI><a href="#[de]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;LCD_SetCursor
</UL>
<BR>[Called By]<UL><LI><a href="#[e3]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;LCD_DrawRect
</UL>

<P><STRONG><a name="[e3]"></a>LCD_DrawRect</STRONG> (Thumb, 68 bytes, Stack size 24 bytes, tft_lcd.o(i.LCD_DrawRect))
<BR><BR>[Stack]<UL><LI>Max Depth = 64<LI>Call Chain = LCD_DrawRect &rArr; LCD_DrawLine &rArr; LCD_SetCursor
</UL>
<BR>[Calls]<UL><LI><a href="#[e1]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;LCD_DrawLine
</UL>
<BR>[Called By]<UL><LI><a href="#[100]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;My_Disp_Main
</UL>

<P><STRONG><a name="[e4]"></a>LCD_DrawRectS</STRONG> (Thumb, 58 bytes, Stack size 32 bytes, tft_lcd.o(i.LCD_DrawRectS))
<BR><BR>[Stack]<UL><LI>Max Depth = 136<LI>Call Chain = LCD_DrawRectS &rArr; LCD_DrawuniLine &rArr; PutPixel &rArr; LCD_SetCursor
</UL>
<BR>[Calls]<UL><LI><a href="#[e5]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;LCD_DrawuniLine
</UL>
<BR>[Called By]<UL><LI><a href="#[112]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;OS_Rect_Draw
</UL>

<P><STRONG><a name="[e5]"></a>LCD_DrawuniLine</STRONG> (Thumb, 286 bytes, Stack size 72 bytes, tft_lcd.o(i.LCD_DrawuniLine))
<BR><BR>[Stack]<UL><LI>Max Depth = 104<LI>Call Chain = LCD_DrawuniLine &rArr; PutPixel &rArr; LCD_SetCursor
</UL>
<BR>[Calls]<UL><LI><a href="#[e6]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;PutPixel
</UL>
<BR>[Called By]<UL><LI><a href="#[e4]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;LCD_DrawRectS
</UL>

<P><STRONG><a name="[e7]"></a>LCD_FSMCConfig</STRONG> (Thumb, 94 bytes, Stack size 96 bytes, tft_lcd.o(i.LCD_FSMCConfig))
<BR><BR>[Stack]<UL><LI>Max Depth = 96<LI>Call Chain = LCD_FSMCConfig
</UL>
<BR>[Calls]<UL><LI><a href="#[e8]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;RCC_AHB3PeriphClockCmd
<LI><a href="#[e9]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;FSMC_NORSRAMInit
<LI><a href="#[ea]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;FSMC_NORSRAMCmd
</UL>
<BR>[Called By]<UL><LI><a href="#[10e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;TFT_LCD_Init
</UL>

<P><STRONG><a name="[eb]"></a>LCD_REG_Select</STRONG> (Thumb, 8 bytes, Stack size 0 bytes, tft_lcd.o(i.LCD_REG_Select))
<BR><BR>[Called By]<UL><LI><a href="#[10e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;TFT_LCD_Init
<LI><a href="#[df]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;LCD_WriteRAM_Prepare
<LI><a href="#[de]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;LCD_SetCursor
<LI><a href="#[ec]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;LCD_SetDisplayWindow
</UL>

<P><STRONG><a name="[de]"></a>LCD_SetCursor</STRONG> (Thumb, 68 bytes, Stack size 16 bytes, tft_lcd.o(i.LCD_SetCursor))
<BR><BR>[Stack]<UL><LI>Max Depth = 16<LI>Call Chain = LCD_SetCursor
</UL>
<BR>[Calls]<UL><LI><a href="#[e2]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;LCD_WriteRAM
<LI><a href="#[eb]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;LCD_REG_Select
</UL>
<BR>[Called By]<UL><LI><a href="#[106]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;OS_Point_Draw
<LI><a href="#[d4]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;LCD_Clear
<LI><a href="#[bb]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;LCD_Appoint_Clear
<LI><a href="#[e1]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;LCD_DrawLine
<LI><a href="#[e6]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;PutPixel
</UL>

<P><STRONG><a name="[ec]"></a>LCD_SetDisplayWindow</STRONG> (Thumb, 76 bytes, Stack size 24 bytes, tft_lcd.o(i.LCD_SetDisplayWindow))
<BR><BR>[Stack]<UL><LI>Max Depth = 24<LI>Call Chain = LCD_SetDisplayWindow
</UL>
<BR>[Calls]<UL><LI><a href="#[e2]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;LCD_WriteRAM
<LI><a href="#[eb]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;LCD_REG_Select
</UL>
<BR>[Called By]<UL><LI><a href="#[10e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;TFT_LCD_Init
</UL>

<P><STRONG><a name="[e2]"></a>LCD_WriteRAM</STRONG> (Thumb, 8 bytes, Stack size 0 bytes, tft_lcd.o(i.LCD_WriteRAM))
<BR><BR>[Called By]<UL><LI><a href="#[106]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;OS_Point_Draw
<LI><a href="#[10e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;TFT_LCD_Init
<LI><a href="#[de]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;LCD_SetCursor
<LI><a href="#[ec]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;LCD_SetDisplayWindow
<LI><a href="#[e1]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;LCD_DrawLine
<LI><a href="#[e6]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;PutPixel
</UL>

<P><STRONG><a name="[df]"></a>LCD_WriteRAM_Prepare</STRONG> (Thumb, 10 bytes, Stack size 8 bytes, tft_lcd.o(i.LCD_WriteRAM_Prepare))
<BR><BR>[Stack]<UL><LI>Max Depth = 8<LI>Call Chain = LCD_WriteRAM_Prepare
</UL>
<BR>[Calls]<UL><LI><a href="#[eb]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;LCD_REG_Select
</UL>
<BR>[Called By]<UL><LI><a href="#[106]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;OS_Point_Draw
<LI><a href="#[d4]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;LCD_Clear
<LI><a href="#[bb]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;LCD_Appoint_Clear
<LI><a href="#[e1]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;LCD_DrawLine
<LI><a href="#[e6]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;PutPixel
</UL>

<P><STRONG><a name="[ee]"></a>LED_Control</STRONG> (Thumb, 152 bytes, Stack size 0 bytes, app_led.o(i.LED_Control))
<BR><BR>[Called By]<UL><LI><a href="#[68]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;LED_main
</UL>

<P><STRONG><a name="[ed]"></a>LED_Init</STRONG> (Thumb, 70 bytes, Stack size 16 bytes, drive_gpio.o(i.LED_Init))
<BR><BR>[Stack]<UL><LI>Max Depth = 36<LI>Call Chain = LED_Init &rArr; GPIO_Init
</UL>
<BR>[Calls]<UL><LI><a href="#[c6]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;RCC_AHB1PeriphClockCmd
<LI><a href="#[c7]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;GPIO_Init
</UL>
<BR>[Called By]<UL><LI><a href="#[10a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;OS_Init
</UL>

<P><STRONG><a name="[68]"></a>LED_main</STRONG> (Thumb, 40 bytes, Stack size 0 bytes, app_led.o(i.LED_main))
<BR><BR>[Stack]<UL><LI>Max Depth = 24 + Unknown Stack Size
<LI>Call Chain = LED_main &rArr; OSTimeDly &rArr; OS_Sched
</UL>
<BR>[Calls]<UL><LI><a href="#[ee]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;LED_Control
<LI><a href="#[ef]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;OSTimeDly
</UL>
<BR>[Address Reference Count : 1]<UL><LI> main.o(i.main)
</UL>
<P><STRONG><a name="[7]"></a>MemManage_Handler</STRONG> (Thumb, 4 bytes, Stack size 0 bytes, stm32f4xx_it.o(i.MemManage_Handler))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f40_41xxx.o(RESET)
</UL>
<P><STRONG><a name="[f0]"></a>MenuHaddler_1</STRONG> (Thumb, 1976 bytes, Stack size 104 bytes, user.o(i.MenuHaddler_1))
<BR><BR>[Stack]<UL><LI>Max Depth = 632 + Unknown Stack Size
<LI>Call Chain = MenuHaddler_1 &rArr; PS2_ReadNum &rArr; OS_Num_Show &rArr; OS_String_Show &rArr; OS_Font_Show &rArr; OS_HzMat_Get &rArr; W25Q64_Read &rArr; SPI1_ReadWriteByte
</UL>
<BR>[Calls]<UL><LI><a href="#[a6]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;delay_ms
<LI><a href="#[bb]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;LCD_Appoint_Clear
<LI><a href="#[f2]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;DAC_SetChannel1Data
<LI><a href="#[f1]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Show_Val
<LI><a href="#[f3]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;PS2_ReadNum
<LI><a href="#[ba]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Change_Menu
<LI><a href="#[a3]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;AD9959_senddata
<LI><a href="#[f4]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_f2d
<LI><a href="#[f5]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_dmul
<LI><a href="#[f9]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_ddiv
<LI><a href="#[fa]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_dsub
<LI><a href="#[f6]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_drsub
<LI><a href="#[fb]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_dadd
<LI><a href="#[f7]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_d2f
<LI><a href="#[f8]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__hardfp_sqrt
</UL>
<BR>[Called By]<UL><LI><a href="#[66]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;User_main
</UL>

<P><STRONG><a name="[fc]"></a>MenuHaddler_2</STRONG> (Thumb, 32 bytes, Stack size 8 bytes, user.o(i.MenuHaddler_2))
<BR><BR>[Stack]<UL><LI>Max Depth = 336 + Unknown Stack Size
<LI>Call Chain = MenuHaddler_2 &rArr; Change_Menu &rArr; OS_String_Show &rArr; OS_Font_Show &rArr; OS_HzMat_Get &rArr; W25Q64_Read &rArr; SPI1_ReadWriteByte
</UL>
<BR>[Calls]<UL><LI><a href="#[a6]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;delay_ms
<LI><a href="#[ba]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Change_Menu
</UL>
<BR>[Called By]<UL><LI><a href="#[66]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;User_main
</UL>

<P><STRONG><a name="[fd]"></a>MenuHaddler_3</STRONG> (Thumb, 50 bytes, Stack size 8 bytes, user.o(i.MenuHaddler_3))
<BR><BR>[Stack]<UL><LI>Max Depth = 336 + Unknown Stack Size
<LI>Call Chain = MenuHaddler_3 &rArr; Change_Menu &rArr; OS_String_Show &rArr; OS_Font_Show &rArr; OS_HzMat_Get &rArr; W25Q64_Read &rArr; SPI1_ReadWriteByte
</UL>
<BR>[Calls]<UL><LI><a href="#[a6]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;delay_ms
<LI><a href="#[ba]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Change_Menu
<LI><a href="#[fe]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Start_ADC_Sampling
<LI><a href="#[b9]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Set_SamplingFre
<LI><a href="#[d2]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;IIR_Filter_Enable
</UL>
<BR>[Called By]<UL><LI><a href="#[66]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;User_main
</UL>

<P><STRONG><a name="[ff]"></a>MenuHaddler_4</STRONG> (Thumb, 26 bytes, Stack size 8 bytes, user.o(i.MenuHaddler_4))
<BR><BR>[Stack]<UL><LI>Max Depth = 336<LI>Call Chain = MenuHaddler_4 &rArr; Change_Menu &rArr; OS_String_Show &rArr; OS_Font_Show &rArr; OS_HzMat_Get &rArr; W25Q64_Read &rArr; SPI1_ReadWriteByte
</UL>
<BR>[Calls]<UL><LI><a href="#[ba]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Change_Menu
</UL>
<BR>[Called By]<UL><LI><a href="#[66]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;User_main
</UL>

<P><STRONG><a name="[67]"></a>MyPs2KeyScan</STRONG> (Thumb, 456 bytes, Stack size 0 bytes, drive_ps2.o(i.MyPs2KeyScan))
<BR><BR>[Stack]<UL><LI>Max Depth = 68 + Unknown Stack Size
<LI>Call Chain = MyPs2KeyScan &rArr; Key_StateSweep &rArr; PS2_ReadKeyCodon &rArr; PS2_SCL_Wait &rArr; PS2_SCL_Set &rArr; GPIO_Init
</UL>
<BR>[Calls]<UL><LI><a href="#[a6]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;delay_ms
<LI><a href="#[dc]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Key_StateSweep
</UL>
<BR>[Address Reference Count : 1]<UL><LI> main.o(i.main)
</UL>
<P><STRONG><a name="[100]"></a>My_Disp_Main</STRONG> (Thumb, 198 bytes, Stack size 16 bytes, user.o(i.My_Disp_Main))
<BR><BR>[Stack]<UL><LI>Max Depth = 328<LI>Call Chain = My_Disp_Main &rArr; OS_String_Show &rArr; OS_Font_Show &rArr; OS_HzMat_Get &rArr; W25Q64_Read &rArr; SPI1_ReadWriteByte
</UL>
<BR>[Calls]<UL><LI><a href="#[bc]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;OS_String_Show
<LI><a href="#[e3]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;LCD_DrawRect
</UL>
<BR>[Called By]<UL><LI><a href="#[148]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;menu_show
<LI><a href="#[66]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;User_main
</UL>

<P><STRONG><a name="[5]"></a>NMI_Handler</STRONG> (Thumb, 2 bytes, Stack size 0 bytes, stm32f4xx_it.o(i.NMI_Handler))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f40_41xxx.o(RESET)
</UL>
<P><STRONG><a name="[a9]"></a>NVIC_Init</STRONG> (Thumb, 106 bytes, Stack size 16 bytes, misc.o(i.NVIC_Init))
<BR><BR>[Stack]<UL><LI>Max Depth = 16<LI>Call Chain = NVIC_Init
</UL>
<BR>[Called By]<UL><LI><a href="#[11e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;PC1_NVIC_Config
<LI><a href="#[a7]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ADC_DMA_NVIC_Init
</UL>

<P><STRONG><a name="[a8]"></a>NVIC_PriorityGroupConfig</STRONG> (Thumb, 10 bytes, Stack size 0 bytes, misc.o(i.NVIC_PriorityGroupConfig))
<BR><BR>[Called By]<UL><LI><a href="#[11e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;PC1_NVIC_Config
<LI><a href="#[a7]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ADC_DMA_NVIC_Init
</UL>

<P><STRONG><a name="[102]"></a>OSDelPrioRdy</STRONG> (Thumb, 16 bytes, Stack size 0 bytes, os_cpu.o(i.OSDelPrioRdy))
<BR><BR>[Called By]<UL><LI><a href="#[ef]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;OSTimeDly
</UL>

<P><STRONG><a name="[113]"></a>OSGetHighRdy</STRONG> (Thumb, 32 bytes, Stack size 0 bytes, os_cpu.o(i.OSGetHighRdy))
<BR><BR>[Called By]<UL><LI><a href="#[117]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;OS_Start
<LI><a href="#[104]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;OS_Sched
</UL>

<P><STRONG><a name="[136]"></a>OSSetPrioRdy</STRONG> (Thumb, 16 bytes, Stack size 0 bytes, os_cpu.o(i.OSSetPrioRdy))
<BR><BR>[Called By]<UL><LI><a href="#[118]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Task_Create
<LI><a href="#[d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SysTick_Handler
</UL>

<P><STRONG><a name="[ef]"></a>OSTimeDly</STRONG> (Thumb, 50 bytes, Stack size 16 bytes, os_cpu.o(i.OSTimeDly))
<BR><BR>[Stack]<UL><LI>Max Depth = 24 + Unknown Stack Size
<LI>Call Chain = OSTimeDly &rArr; OS_Sched
</UL>
<BR>[Calls]<UL><LI><a href="#[101]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;OS_CPU_SR_Save
<LI><a href="#[103]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;OS_CPU_SR_Restore
<LI><a href="#[104]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;OS_Sched
<LI><a href="#[102]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;OSDelPrioRdy
</UL>
<BR>[Called By]<UL><LI><a href="#[a6]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;delay_ms
<LI><a href="#[68]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;LED_main
</UL>

<P><STRONG><a name="[105]"></a>OS_Char_Show</STRONG> (Thumb, 266 bytes, Stack size 40 bytes, os_ui.o(i.OS_Char_Show))
<BR><BR>[Stack]<UL><LI>Max Depth = 72<LI>Call Chain = OS_Char_Show &rArr; OS_Point_Draw &rArr; LCD_SetCursor
</UL>
<BR>[Calls]<UL><LI><a href="#[106]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;OS_Point_Draw
</UL>
<BR>[Called By]<UL><LI><a href="#[bc]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;OS_String_Show
</UL>

<P><STRONG><a name="[107]"></a>OS_Font_Show</STRONG> (Thumb, 192 bytes, Stack size 200 bytes, os_ui.o(i.OS_Font_Show))
<BR><BR>[Stack]<UL><LI>Max Depth = 272<LI>Call Chain = OS_Font_Show &rArr; OS_HzMat_Get &rArr; W25Q64_Read &rArr; SPI1_ReadWriteByte
</UL>
<BR>[Calls]<UL><LI><a href="#[106]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;OS_Point_Draw
<LI><a href="#[108]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;OS_HzMat_Get
</UL>
<BR>[Called By]<UL><LI><a href="#[bc]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;OS_String_Show
</UL>

<P><STRONG><a name="[108]"></a>OS_HzMat_Get</STRONG> (Thumb, 218 bytes, Stack size 40 bytes, os_ui.o(i.OS_HzMat_Get))
<BR><BR>[Stack]<UL><LI>Max Depth = 72<LI>Call Chain = OS_HzMat_Get &rArr; W25Q64_Read &rArr; SPI1_ReadWriteByte
</UL>
<BR>[Calls]<UL><LI><a href="#[109]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;W25Q64_Read
</UL>
<BR>[Called By]<UL><LI><a href="#[107]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;OS_Font_Show
</UL>

<P><STRONG><a name="[64]"></a>OS_IDLE_Task</STRONG> (Thumb, 8 bytes, Stack size 0 bytes, os_cpu.o(i.OS_IDLE_Task))
<BR>[Address Reference Count : 1]<UL><LI> os_cpu.o(i.OS_Start)
</UL>
<P><STRONG><a name="[10a]"></a>OS_Init</STRONG> (Thumb, 20 bytes, Stack size 8 bytes, main.o(i.OS_Init))
<BR><BR>[Stack]<UL><LI>Max Depth = 328 + Unknown Stack Size
<LI>Call Chain = OS_Init &rArr; OS_LCD_Init &rArr; OS_String_Show &rArr; OS_Font_Show &rArr; OS_HzMat_Get &rArr; W25Q64_Read &rArr; SPI1_ReadWriteByte
</UL>
<BR>[Calls]<UL><LI><a href="#[10b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;System_init
<LI><a href="#[10c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;PS2_Keyboard_Init
<LI><a href="#[10d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;OS_LCD_Init
<LI><a href="#[ed]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;LED_Init
</UL>
<BR>[Called By]<UL><LI><a href="#[7f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;main
</UL>

<P><STRONG><a name="[10d]"></a>OS_LCD_Init</STRONG> (Thumb, 58 bytes, Stack size 8 bytes, os_ui.o(i.OS_LCD_Init))
<BR><BR>[Stack]<UL><LI>Max Depth = 320 + Unknown Stack Size
<LI>Call Chain = OS_LCD_Init &rArr; OS_String_Show &rArr; OS_Font_Show &rArr; OS_HzMat_Get &rArr; W25Q64_Read &rArr; SPI1_ReadWriteByte
</UL>
<BR>[Calls]<UL><LI><a href="#[bc]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;OS_String_Show
<LI><a href="#[d4]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;LCD_Clear
<LI><a href="#[10f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;font_init
<LI><a href="#[10e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;TFT_LCD_Init
</UL>
<BR>[Called By]<UL><LI><a href="#[10a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;OS_Init
</UL>

<P><STRONG><a name="[110]"></a>OS_Line_Draw</STRONG> (Thumb, 198 bytes, Stack size 72 bytes, os_ui.o(i.OS_Line_Draw))
<BR><BR>[Stack]<UL><LI>Max Depth = 104<LI>Call Chain = OS_Line_Draw &rArr; OS_Point_Draw &rArr; LCD_SetCursor
</UL>
<BR>[Calls]<UL><LI><a href="#[106]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;OS_Point_Draw
</UL>
<BR>[Called By]<UL><LI><a href="#[112]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;OS_Rect_Draw
</UL>

<P><STRONG><a name="[111]"></a>OS_Num_Show</STRONG> (Thumb, 92 bytes, Stack size 136 bytes, os_ui.o(i.OS_Num_Show))
<BR><BR>[Stack]<UL><LI>Max Depth = 448 + Unknown Stack Size
<LI>Call Chain = OS_Num_Show &rArr; OS_String_Show &rArr; OS_Font_Show &rArr; OS_HzMat_Get &rArr; W25Q64_Read &rArr; SPI1_ReadWriteByte
</UL>
<BR>[Calls]<UL><LI><a href="#[bc]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;OS_String_Show
<LI><a href="#[86]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_memclr4
<LI><a href="#[87]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__2sprintf
</UL>
<BR>[Called By]<UL><LI><a href="#[f1]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Show_Val
<LI><a href="#[f3]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;PS2_ReadNum
</UL>

<P><STRONG><a name="[106]"></a>OS_Point_Draw</STRONG> (Thumb, 30 bytes, Stack size 16 bytes, os_ui.o(i.OS_Point_Draw))
<BR><BR>[Stack]<UL><LI>Max Depth = 32<LI>Call Chain = OS_Point_Draw &rArr; LCD_SetCursor
</UL>
<BR>[Calls]<UL><LI><a href="#[df]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;LCD_WriteRAM_Prepare
<LI><a href="#[e2]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;LCD_WriteRAM
<LI><a href="#[de]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;LCD_SetCursor
</UL>
<BR>[Called By]<UL><LI><a href="#[110]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;OS_Line_Draw
<LI><a href="#[107]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;OS_Font_Show
<LI><a href="#[105]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;OS_Char_Show
</UL>

<P><STRONG><a name="[112]"></a>OS_Rect_Draw</STRONG> (Thumb, 146 bytes, Stack size 40 bytes, os_ui.o(i.OS_Rect_Draw))
<BR><BR>[Stack]<UL><LI>Max Depth = 176<LI>Call Chain = OS_Rect_Draw &rArr; LCD_DrawRectS &rArr; LCD_DrawuniLine &rArr; PutPixel &rArr; LCD_SetCursor
</UL>
<BR>[Calls]<UL><LI><a href="#[110]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;OS_Line_Draw
<LI><a href="#[e4]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;LCD_DrawRectS
</UL>
<BR>[Called By]<UL><LI><a href="#[f3]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;PS2_ReadNum
</UL>

<P><STRONG><a name="[104]"></a>OS_Sched</STRONG> (Thumb, 68 bytes, Stack size 8 bytes, os_cpu.o(i.OS_Sched))
<BR><BR>[Stack]<UL><LI>Max Depth = 8 + Unknown Stack Size
<LI>Call Chain = OS_Sched
</UL>
<BR>[Calls]<UL><LI><a href="#[101]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;OS_CPU_SR_Save
<LI><a href="#[103]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;OS_CPU_SR_Restore
<LI><a href="#[114]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;OSCtxSw
<LI><a href="#[113]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;OSGetHighRdy
</UL>
<BR>[Called By]<UL><LI><a href="#[ef]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;OSTimeDly
<LI><a href="#[d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SysTick_Handler
</UL>

<P><STRONG><a name="[115]"></a>OS_SchedLock</STRONG> (Thumb, 22 bytes, Stack size 8 bytes, os_cpu.o(i.OS_SchedLock))
<BR><BR>[Stack]<UL><LI>Max Depth = 8 + Unknown Stack Size
<LI>Call Chain = OS_SchedLock
</UL>
<BR>[Calls]<UL><LI><a href="#[101]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;OS_CPU_SR_Save
<LI><a href="#[103]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;OS_CPU_SR_Restore
</UL>
<BR>[Called By]<UL><LI><a href="#[147]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;delay_us
</UL>

<P><STRONG><a name="[116]"></a>OS_SchedUnlock</STRONG> (Thumb, 22 bytes, Stack size 8 bytes, os_cpu.o(i.OS_SchedUnlock))
<BR><BR>[Stack]<UL><LI>Max Depth = 8 + Unknown Stack Size
<LI>Call Chain = OS_SchedUnlock
</UL>
<BR>[Calls]<UL><LI><a href="#[101]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;OS_CPU_SR_Save
<LI><a href="#[103]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;OS_CPU_SR_Restore
</UL>
<BR>[Called By]<UL><LI><a href="#[147]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;delay_us
</UL>

<P><STRONG><a name="[117]"></a>OS_Start</STRONG> (Thumb, 66 bytes, Stack size 8 bytes, os_cpu.o(i.OS_Start))
<BR><BR>[Stack]<UL><LI>Max Depth = 32 + Unknown Stack Size
<LI>Call Chain = OS_Start &rArr; Task_Create
</UL>
<BR>[Calls]<UL><LI><a href="#[118]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Task_Create
<LI><a href="#[119]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;OSStartHighRdy
<LI><a href="#[113]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;OSGetHighRdy
</UL>
<BR>[Called By]<UL><LI><a href="#[7f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;main
</UL>

<P><STRONG><a name="[bc]"></a>OS_String_Show</STRONG> (Thumb, 124 bytes, Stack size 40 bytes, os_ui.o(i.OS_String_Show))
<BR><BR>[Stack]<UL><LI>Max Depth = 312<LI>Call Chain = OS_String_Show &rArr; OS_Font_Show &rArr; OS_HzMat_Get &rArr; W25Q64_Read &rArr; SPI1_ReadWriteByte
</UL>
<BR>[Calls]<UL><LI><a href="#[107]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;OS_Font_Show
<LI><a href="#[105]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;OS_Char_Show
</UL>
<BR>[Called By]<UL><LI><a href="#[10d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;OS_LCD_Init
<LI><a href="#[111]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;OS_Num_Show
<LI><a href="#[f1]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Show_Val
<LI><a href="#[100]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;My_Disp_Main
<LI><a href="#[ba]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Change_Menu
</UL>

<P><STRONG><a name="[11a]"></a>PC1_EXTI_Config</STRONG> (Thumb, 48 bytes, Stack size 16 bytes, user_adc.o(i.PC1_EXTI_Config))
<BR><BR>[Stack]<UL><LI>Max Depth = 28<LI>Call Chain = PC1_EXTI_Config &rArr; SYSCFG_EXTILineConfig
</UL>
<BR>[Calls]<UL><LI><a href="#[11b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SYSCFG_EXTILineConfig
<LI><a href="#[ab]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;RCC_APB2PeriphClockCmd
<LI><a href="#[11c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;EXTI_Init
</UL>
<BR>[Called By]<UL><LI><a href="#[d8]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;PC1_Init
</UL>

<P><STRONG><a name="[11d]"></a>PC1_GPIO_Config</STRONG> (Thumb, 42 bytes, Stack size 16 bytes, user_adc.o(i.PC1_GPIO_Config))
<BR><BR>[Stack]<UL><LI>Max Depth = 36<LI>Call Chain = PC1_GPIO_Config &rArr; GPIO_Init
</UL>
<BR>[Calls]<UL><LI><a href="#[c6]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;RCC_AHB1PeriphClockCmd
<LI><a href="#[c7]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;GPIO_Init
</UL>
<BR>[Called By]<UL><LI><a href="#[d8]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;PC1_Init
</UL>

<P><STRONG><a name="[d8]"></a>PC1_Init</STRONG> (Thumb, 16 bytes, Stack size 8 bytes, user_adc.o(i.PC1_Init))
<BR><BR>[Stack]<UL><LI>Max Depth = 44<LI>Call Chain = PC1_Init &rArr; PC1_GPIO_Config &rArr; GPIO_Init
</UL>
<BR>[Calls]<UL><LI><a href="#[11e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;PC1_NVIC_Config
<LI><a href="#[11d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;PC1_GPIO_Config
<LI><a href="#[11a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;PC1_EXTI_Config
</UL>
<BR>[Called By]<UL><LI><a href="#[d3]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Init_All
</UL>

<P><STRONG><a name="[11e]"></a>PC1_NVIC_Config</STRONG> (Thumb, 40 bytes, Stack size 8 bytes, user_adc.o(i.PC1_NVIC_Config))
<BR><BR>[Stack]<UL><LI>Max Depth = 24<LI>Call Chain = PC1_NVIC_Config &rArr; NVIC_Init
</UL>
<BR>[Calls]<UL><LI><a href="#[a8]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;NVIC_PriorityGroupConfig
<LI><a href="#[a9]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;NVIC_Init
</UL>
<BR>[Called By]<UL><LI><a href="#[d8]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;PC1_Init
</UL>

<P><STRONG><a name="[d6]"></a>PGA2310_Init</STRONG> (Thumb, 22 bytes, Stack size 8 bytes, user_pga2310.o(i.PGA2310_Init))
<BR><BR>[Stack]<UL><LI>Max Depth = 104 + Unknown Stack Size
<LI>Call Chain = PGA2310_Init &rArr; PGA2310_SetAv &rArr; User_SPI_SendData &rArr; delay_us &rArr; OS_SchedUnlock
</UL>
<BR>[Calls]<UL><LI><a href="#[120]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;PGA2310_SetAv
<LI><a href="#[11f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SPI_GPIO_Init
</UL>
<BR>[Called By]<UL><LI><a href="#[d3]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Init_All
</UL>

<P><STRONG><a name="[120]"></a>PGA2310_SetAv</STRONG> (Thumb, 204 bytes, Stack size 32 bytes, user_pga2310.o(i.PGA2310_SetAv))
<BR><BR>[Stack]<UL><LI>Max Depth = 96 + Unknown Stack Size
<LI>Call Chain = PGA2310_SetAv &rArr; User_SPI_SendData &rArr; delay_us &rArr; OS_SchedUnlock
</UL>
<BR>[Calls]<UL><LI><a href="#[123]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;User_SPI_SendData
<LI><a href="#[f4]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_f2d
<LI><a href="#[121]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_cdrcmple
<LI><a href="#[122]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_cdcmple
</UL>
<BR>[Called By]<UL><LI><a href="#[d6]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;PGA2310_Init
</UL>

<P><STRONG><a name="[10c]"></a>PS2_Keyboard_Init</STRONG> (Thumb, 8 bytes, Stack size 8 bytes, drive_ps2.o(i.PS2_Keyboard_Init))
<BR><BR>[Stack]<UL><LI>Max Depth = 44<LI>Call Chain = PS2_Keyboard_Init &rArr; PS2_GPIO_Init &rArr; GPIO_Init
</UL>
<BR>[Calls]<UL><LI><a href="#[124]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;PS2_GPIO_Init
</UL>
<BR>[Called By]<UL><LI><a href="#[10a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;OS_Init
</UL>

<P><STRONG><a name="[dd]"></a>PS2_ReadKeyCodon</STRONG> (Thumb, 170 bytes, Stack size 16 bytes, drive_ps2.o(i.PS2_ReadKeyCodon))
<BR><BR>[Stack]<UL><LI>Max Depth = 60<LI>Call Chain = PS2_ReadKeyCodon &rArr; PS2_SCL_Wait &rArr; PS2_SCL_Set &rArr; GPIO_Init
</UL>
<BR>[Calls]<UL><LI><a href="#[127]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;GPIO_ReadInputDataBit
<LI><a href="#[126]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;PS2_SCL_Wait
<LI><a href="#[125]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;PS2_SCL_Set
</UL>
<BR>[Called By]<UL><LI><a href="#[dc]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Key_StateSweep
</UL>

<P><STRONG><a name="[f3]"></a>PS2_ReadNum</STRONG> (Thumb, 416 bytes, Stack size 80 bytes, user.o(i.PS2_ReadNum))
<BR><BR>[Stack]<UL><LI>Max Depth = 528 + Unknown Stack Size
<LI>Call Chain = PS2_ReadNum &rArr; OS_Num_Show &rArr; OS_String_Show &rArr; OS_Font_Show &rArr; OS_HzMat_Get &rArr; W25Q64_Read &rArr; SPI1_ReadWriteByte
</UL>
<BR>[Calls]<UL><LI><a href="#[112]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;OS_Rect_Draw
<LI><a href="#[111]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;OS_Num_Show
<LI><a href="#[bb]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;LCD_Appoint_Clear
<LI><a href="#[b8]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;TIM_Cmd
<LI><a href="#[129]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__hardfp_pow
<LI><a href="#[f4]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_f2d
<LI><a href="#[128]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_ui2d
<LI><a href="#[f9]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_ddiv
<LI><a href="#[fb]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_dadd
<LI><a href="#[f7]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_d2f
</UL>
<BR>[Called By]<UL><LI><a href="#[f0]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;MenuHaddler_1
</UL>

<P><STRONG><a name="[e6]"></a>PutPixel</STRONG> (Thumb, 28 bytes, Stack size 16 bytes, tft_lcd.o(i.PutPixel))
<BR><BR>[Stack]<UL><LI>Max Depth = 32<LI>Call Chain = PutPixel &rArr; LCD_SetCursor
</UL>
<BR>[Calls]<UL><LI><a href="#[df]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;LCD_WriteRAM_Prepare
<LI><a href="#[e2]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;LCD_WriteRAM
<LI><a href="#[de]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;LCD_SetCursor
</UL>
<BR>[Called By]<UL><LI><a href="#[e5]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;LCD_DrawuniLine
</UL>

<P><STRONG><a name="[c6]"></a>RCC_AHB1PeriphClockCmd</STRONG> (Thumb, 26 bytes, Stack size 0 bytes, stm32f4xx_rcc.o(i.RCC_AHB1PeriphClockCmd))
<BR><BR>[Called By]<UL><LI><a href="#[ed]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;LED_Init
<LI><a href="#[d7]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Init_Uart
<LI><a href="#[c5]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;GPIO_Config
<LI><a href="#[13d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;User_ADC_GPIO_Init
<LI><a href="#[11d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;PC1_GPIO_Config
<LI><a href="#[c8]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;IIR_ADC_DMA_Init
<LI><a href="#[144]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;User_DAC_GPIO_Init
<LI><a href="#[143]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;User_DAC_DMA_Init
<LI><a href="#[125]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;PS2_SCL_Set
<LI><a href="#[124]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;PS2_GPIO_Init
<LI><a href="#[11f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SPI_GPIO_Init
<LI><a href="#[e0]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;LCD_CtrlLinesConfig
<LI><a href="#[12c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SPI1_Init
<LI><a href="#[149]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;W25Q64_Init
</UL>

<P><STRONG><a name="[e8]"></a>RCC_AHB3PeriphClockCmd</STRONG> (Thumb, 26 bytes, Stack size 0 bytes, stm32f4xx_rcc.o(i.RCC_AHB3PeriphClockCmd))
<BR><BR>[Called By]<UL><LI><a href="#[e7]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;LCD_FSMCConfig
</UL>

<P><STRONG><a name="[b4]"></a>RCC_APB1PeriphClockCmd</STRONG> (Thumb, 26 bytes, Stack size 0 bytes, stm32f4xx_rcc.o(i.RCC_APB1PeriphClockCmd))
<BR><BR>[Called By]<UL><LI><a href="#[b3]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ADC_TIM3_Init
<LI><a href="#[145]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;User_DAC_TIM_Init
<LI><a href="#[13e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;User_DAC_Configure
</UL>

<P><STRONG><a name="[be]"></a>RCC_APB1PeriphResetCmd</STRONG> (Thumb, 26 bytes, Stack size 0 bytes, stm32f4xx_rcc.o(i.RCC_APB1PeriphResetCmd))
<BR><BR>[Called By]<UL><LI><a href="#[bd]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;DAC_DeInit
</UL>

<P><STRONG><a name="[ab]"></a>RCC_APB2PeriphClockCmd</STRONG> (Thumb, 26 bytes, Stack size 0 bytes, stm32f4xx_rcc.o(i.RCC_APB2PeriphClockCmd))
<BR><BR>[Called By]<UL><LI><a href="#[d7]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Init_Uart
<LI><a href="#[11a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;PC1_EXTI_Config
<LI><a href="#[b2]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ADC_Mode_Independent_Init
<LI><a href="#[aa]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ADC_DualMode_RegSimult_Init
<LI><a href="#[12c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SPI1_Init
</UL>

<P><STRONG><a name="[12d]"></a>RCC_APB2PeriphResetCmd</STRONG> (Thumb, 26 bytes, Stack size 0 bytes, stm32f4xx_rcc.o(i.RCC_APB2PeriphResetCmd))
<BR><BR>[Called By]<UL><LI><a href="#[12c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SPI1_Init
</UL>

<P><STRONG><a name="[13c]"></a>RCC_GetClocksFreq</STRONG> (Thumb, 214 bytes, Stack size 20 bytes, stm32f4xx_rcc.o(i.RCC_GetClocksFreq))
<BR><BR>[Stack]<UL><LI>Max Depth = 20<LI>Call Chain = RCC_GetClocksFreq
</UL>
<BR>[Called By]<UL><LI><a href="#[da]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;USART_Init
</UL>

<P><STRONG><a name="[12c]"></a>SPI1_Init</STRONG> (Thumb, 198 bytes, Stack size 32 bytes, spi.o(i.SPI1_Init))
<BR><BR>[Stack]<UL><LI>Max Depth = 52<LI>Call Chain = SPI1_Init &rArr; GPIO_Init
</UL>
<BR>[Calls]<UL><LI><a href="#[ab]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;RCC_APB2PeriphClockCmd
<LI><a href="#[c6]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;RCC_AHB1PeriphClockCmd
<LI><a href="#[c7]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;GPIO_Init
<LI><a href="#[12d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;RCC_APB2PeriphResetCmd
<LI><a href="#[d9]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;GPIO_PinAFConfig
<LI><a href="#[12e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SPI_Init
<LI><a href="#[12f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SPI_Cmd
<LI><a href="#[130]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SPI1_ReadWriteByte
</UL>
<BR>[Called By]<UL><LI><a href="#[149]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;W25Q64_Init
</UL>

<P><STRONG><a name="[130]"></a>SPI1_ReadWriteByte</STRONG> (Thumb, 50 bytes, Stack size 8 bytes, spi.o(i.SPI1_ReadWriteByte))
<BR><BR>[Stack]<UL><LI>Max Depth = 8<LI>Call Chain = SPI1_ReadWriteByte
</UL>
<BR>[Calls]<UL><LI><a href="#[132]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SPI_I2S_SendData
<LI><a href="#[133]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SPI_I2S_ReceiveData
<LI><a href="#[131]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SPI_I2S_GetFlagStatus
</UL>
<BR>[Called By]<UL><LI><a href="#[109]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;W25Q64_Read
<LI><a href="#[14a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;W25Q64_ReadID
<LI><a href="#[12c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SPI1_Init
</UL>

<P><STRONG><a name="[134]"></a>SPI1_SetSpeed</STRONG> (Thumb, 36 bytes, Stack size 8 bytes, spi.o(i.SPI1_SetSpeed))
<BR><BR>[Stack]<UL><LI>Max Depth = 8<LI>Call Chain = SPI1_SetSpeed
</UL>
<BR>[Calls]<UL><LI><a href="#[12f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SPI_Cmd
</UL>
<BR>[Called By]<UL><LI><a href="#[149]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;W25Q64_Init
</UL>

<P><STRONG><a name="[12f]"></a>SPI_Cmd</STRONG> (Thumb, 24 bytes, Stack size 0 bytes, stm32f4xx_spi.o(i.SPI_Cmd))
<BR><BR>[Called By]<UL><LI><a href="#[134]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SPI1_SetSpeed
<LI><a href="#[12c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SPI1_Init
</UL>

<P><STRONG><a name="[11f]"></a>SPI_GPIO_Init</STRONG> (Thumb, 138 bytes, Stack size 16 bytes, user_spi.o(i.SPI_GPIO_Init))
<BR><BR>[Stack]<UL><LI>Max Depth = 36<LI>Call Chain = SPI_GPIO_Init &rArr; GPIO_Init
</UL>
<BR>[Calls]<UL><LI><a href="#[c6]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;RCC_AHB1PeriphClockCmd
<LI><a href="#[c7]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;GPIO_Init
<LI><a href="#[d9]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;GPIO_PinAFConfig
</UL>
<BR>[Called By]<UL><LI><a href="#[d6]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;PGA2310_Init
</UL>

<P><STRONG><a name="[131]"></a>SPI_I2S_GetFlagStatus</STRONG> (Thumb, 18 bytes, Stack size 0 bytes, stm32f4xx_spi.o(i.SPI_I2S_GetFlagStatus))
<BR><BR>[Called By]<UL><LI><a href="#[130]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SPI1_ReadWriteByte
</UL>

<P><STRONG><a name="[133]"></a>SPI_I2S_ReceiveData</STRONG> (Thumb, 6 bytes, Stack size 0 bytes, stm32f4xx_spi.o(i.SPI_I2S_ReceiveData))
<BR><BR>[Called By]<UL><LI><a href="#[130]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SPI1_ReadWriteByte
</UL>

<P><STRONG><a name="[132]"></a>SPI_I2S_SendData</STRONG> (Thumb, 4 bytes, Stack size 0 bytes, stm32f4xx_spi.o(i.SPI_I2S_SendData))
<BR><BR>[Called By]<UL><LI><a href="#[130]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SPI1_ReadWriteByte
</UL>

<P><STRONG><a name="[12e]"></a>SPI_Init</STRONG> (Thumb, 60 bytes, Stack size 8 bytes, stm32f4xx_spi.o(i.SPI_Init))
<BR><BR>[Stack]<UL><LI>Max Depth = 8<LI>Call Chain = SPI_Init
</UL>
<BR>[Called By]<UL><LI><a href="#[12c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SPI1_Init
</UL>

<P><STRONG><a name="[a]"></a>SVC_Handler</STRONG> (Thumb, 2 bytes, Stack size 0 bytes, stm32f4xx_it.o(i.SVC_Handler))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f40_41xxx.o(RESET)
</UL>
<P><STRONG><a name="[11b]"></a>SYSCFG_EXTILineConfig</STRONG> (Thumb, 60 bytes, Stack size 12 bytes, stm32f4xx_syscfg.o(i.SYSCFG_EXTILineConfig))
<BR><BR>[Stack]<UL><LI>Max Depth = 12<LI>Call Chain = SYSCFG_EXTILineConfig
</UL>
<BR>[Called By]<UL><LI><a href="#[11a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;PC1_EXTI_Config
</UL>

<P><STRONG><a name="[b9]"></a>Set_SamplingFre</STRONG> (Thumb, 110 bytes, Stack size 24 bytes, user_adc.o(i.Set_SamplingFre))
<BR><BR>[Stack]<UL><LI>Max Depth = 24<LI>Call Chain = Set_SamplingFre
</UL>
<BR>[Calls]<UL><LI><a href="#[b8]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;TIM_Cmd
</UL>
<BR>[Called By]<UL><LI><a href="#[fd]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;MenuHaddler_3
<LI><a href="#[b3]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ADC_TIM3_Init
</UL>

<P><STRONG><a name="[135]"></a>Set_TriggerFre</STRONG> (Thumb, 106 bytes, Stack size 24 bytes, user_dac.o(i.Set_TriggerFre))
<BR><BR>[Stack]<UL><LI>Max Depth = 24<LI>Call Chain = Set_TriggerFre
</UL>
<BR>[Calls]<UL><LI><a href="#[b8]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;TIM_Cmd
</UL>
<BR>[Called By]<UL><LI><a href="#[145]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;User_DAC_TIM_Init
</UL>

<P><STRONG><a name="[f1]"></a>Show_Val</STRONG> (Thumb, 146 bytes, Stack size 32 bytes, user.o(i.Show_Val))
<BR><BR>[Stack]<UL><LI>Max Depth = 480 + Unknown Stack Size
<LI>Call Chain = Show_Val &rArr; OS_Num_Show &rArr; OS_String_Show &rArr; OS_Font_Show &rArr; OS_HzMat_Get &rArr; W25Q64_Read &rArr; SPI1_ReadWriteByte
</UL>
<BR>[Calls]<UL><LI><a href="#[bc]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;OS_String_Show
<LI><a href="#[111]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;OS_Num_Show
<LI><a href="#[f4]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_f2d
</UL>
<BR>[Called By]<UL><LI><a href="#[f0]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;MenuHaddler_1
</UL>

<P><STRONG><a name="[fe]"></a>Start_ADC_Sampling</STRONG> (Thumb, 18 bytes, Stack size 8 bytes, user_adc.o(i.Start_ADC_Sampling))
<BR><BR>[Stack]<UL><LI>Max Depth = 8<LI>Call Chain = Start_ADC_Sampling
</UL>
<BR>[Calls]<UL><LI><a href="#[b8]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;TIM_Cmd
</UL>
<BR>[Called By]<UL><LI><a href="#[fd]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;MenuHaddler_3
</UL>

<P><STRONG><a name="[c3]"></a>Stop_ADC_Sampling</STRONG> (Thumb, 12 bytes, Stack size 8 bytes, user_adc.o(i.Stop_ADC_Sampling))
<BR><BR>[Stack]<UL><LI>Max Depth = 8<LI>Call Chain = Stop_ADC_Sampling
</UL>
<BR>[Calls]<UL><LI><a href="#[b8]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;TIM_Cmd
</UL>
<BR>[Called By]<UL><LI><a href="#[15]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;EXTI1_IRQHandler
</UL>

<P><STRONG><a name="[138]"></a>SysTick_CLKSourceConfig</STRONG> (Thumb, 40 bytes, Stack size 0 bytes, misc.o(i.SysTick_CLKSourceConfig))
<BR><BR>[Called By]<UL><LI><a href="#[10b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;System_init
</UL>

<P><STRONG><a name="[d]"></a>SysTick_Handler</STRONG> (Thumb, 126 bytes, Stack size 16 bytes, os_cpu.o(i.SysTick_Handler))
<BR><BR>[Stack]<UL><LI>Max Depth = 24 + Unknown Stack Size
<LI>Call Chain = SysTick_Handler &rArr; OS_Sched
</UL>
<BR>[Calls]<UL><LI><a href="#[101]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;OS_CPU_SR_Save
<LI><a href="#[103]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;OS_CPU_SR_Restore
<LI><a href="#[104]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;OS_Sched
<LI><a href="#[136]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;OSSetPrioRdy
</UL>
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f40_41xxx.o(RESET)
</UL>
<P><STRONG><a name="[60]"></a>SystemInit</STRONG> (Thumb, 88 bytes, Stack size 8 bytes, system_stm32f4xx.o(i.SystemInit))
<BR><BR>[Stack]<UL><LI>Max Depth = 20<LI>Call Chain = SystemInit &rArr; SetSysClock
</UL>
<BR>[Calls]<UL><LI><a href="#[137]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SetSysClock
</UL>
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f40_41xxx.o(.text)
</UL>
<P><STRONG><a name="[10b]"></a>System_init</STRONG> (Thumb, 76 bytes, Stack size 8 bytes, os_cpu.o(i.System_init))
<BR><BR>[Stack]<UL><LI>Max Depth = 8<LI>Call Chain = System_init
</UL>
<BR>[Calls]<UL><LI><a href="#[138]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SysTick_CLKSourceConfig
</UL>
<BR>[Called By]<UL><LI><a href="#[10a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;OS_Init
</UL>

<P><STRONG><a name="[10e]"></a>TFT_LCD_Init</STRONG> (Thumb, 496 bytes, Stack size 8 bytes, tft_lcd.o(i.TFT_LCD_Init))
<BR><BR>[Stack]<UL><LI>Max Depth = 104 + Unknown Stack Size
<LI>Call Chain = TFT_LCD_Init &rArr; LCD_FSMCConfig
</UL>
<BR>[Calls]<UL><LI><a href="#[a6]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;delay_ms
<LI><a href="#[e2]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;LCD_WriteRAM
<LI><a href="#[ec]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;LCD_SetDisplayWindow
<LI><a href="#[eb]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;LCD_REG_Select
<LI><a href="#[e7]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;LCD_FSMCConfig
<LI><a href="#[e0]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;LCD_CtrlLinesConfig
</UL>
<BR>[Called By]<UL><LI><a href="#[10d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;OS_LCD_Init
</UL>

<P><STRONG><a name="[b8]"></a>TIM_Cmd</STRONG> (Thumb, 24 bytes, Stack size 0 bytes, stm32f4xx_tim.o(i.TIM_Cmd))
<BR><BR>[Called By]<UL><LI><a href="#[f3]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;PS2_ReadNum
<LI><a href="#[c3]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Stop_ADC_Sampling
<LI><a href="#[fe]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Start_ADC_Sampling
<LI><a href="#[b9]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Set_SamplingFre
<LI><a href="#[46]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;DMA2_Stream0_IRQHandler
<LI><a href="#[b3]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ADC_TIM3_Init
<LI><a href="#[145]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;User_DAC_TIM_Init
<LI><a href="#[135]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Set_TriggerFre
</UL>

<P><STRONG><a name="[146]"></a>TIM_DMACmd</STRONG> (Thumb, 18 bytes, Stack size 0 bytes, stm32f4xx_tim.o(i.TIM_DMACmd))
<BR><BR>[Called By]<UL><LI><a href="#[145]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;User_DAC_TIM_Init
</UL>

<P><STRONG><a name="[b6]"></a>TIM_SelectOutputTrigger</STRONG> (Thumb, 18 bytes, Stack size 0 bytes, stm32f4xx_tim.o(i.TIM_SelectOutputTrigger))
<BR><BR>[Called By]<UL><LI><a href="#[b3]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ADC_TIM3_Init
<LI><a href="#[145]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;User_DAC_TIM_Init
</UL>

<P><STRONG><a name="[b5]"></a>TIM_TimeBaseInit</STRONG> (Thumb, 104 bytes, Stack size 0 bytes, stm32f4xx_tim.o(i.TIM_TimeBaseInit))
<BR><BR>[Called By]<UL><LI><a href="#[b3]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ADC_TIM3_Init
<LI><a href="#[145]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;User_DAC_TIM_Init
</UL>

<P><STRONG><a name="[b7]"></a>TIM_UpdateDisableConfig</STRONG> (Thumb, 24 bytes, Stack size 0 bytes, stm32f4xx_tim.o(i.TIM_UpdateDisableConfig))
<BR><BR>[Called By]<UL><LI><a href="#[b3]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ADC_TIM3_Init
<LI><a href="#[145]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;User_DAC_TIM_Init
</UL>

<P><STRONG><a name="[118]"></a>Task_Create</STRONG> (Thumb, 212 bytes, Stack size 24 bytes, os_cpu.o(i.Task_Create))
<BR><BR>[Stack]<UL><LI>Max Depth = 24<LI>Call Chain = Task_Create
</UL>
<BR>[Calls]<UL><LI><a href="#[136]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;OSSetPrioRdy
</UL>
<BR>[Called By]<UL><LI><a href="#[117]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;OS_Start
<LI><a href="#[7f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;main
</UL>

<P><STRONG><a name="[65]"></a>Task_End</STRONG> (Thumb, 4 bytes, Stack size 0 bytes, os_cpu.o(i.Task_End))
<BR>[Address Reference Count : 1]<UL><LI> os_cpu.o(i.Task_Create)
</UL>
<P><STRONG><a name="[33]"></a>USART1_IRQHandler</STRONG> (Thumb, 122 bytes, Stack size 8 bytes, usart.o(i.USART1_IRQHandler))
<BR><BR>[Stack]<UL><LI>Max Depth = 24<LI>Call Chain = USART1_IRQHandler &rArr; USART_GetITStatus
</UL>
<BR>[Calls]<UL><LI><a href="#[13a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;USART_ReceiveData
<LI><a href="#[139]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;USART_GetITStatus
</UL>
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f40_41xxx.o(RESET)
</UL>
<P><STRONG><a name="[55]"></a>USART6_IRQHandler</STRONG> (Thumb, 34 bytes, Stack size 8 bytes, drive_communication.o(i.USART6_IRQHandler))
<BR><BR>[Stack]<UL><LI>Max Depth = 24<LI>Call Chain = USART6_IRQHandler &rArr; USART_GetITStatus
</UL>
<BR>[Calls]<UL><LI><a href="#[13a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;USART_ReceiveData
<LI><a href="#[139]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;USART_GetITStatus
<LI><a href="#[13b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;USART_ClearITPendingBit
</UL>
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f40_41xxx.o(RESET)
</UL>
<P><STRONG><a name="[13b]"></a>USART_ClearITPendingBit</STRONG> (Thumb, 30 bytes, Stack size 8 bytes, stm32f4xx_usart.o(i.USART_ClearITPendingBit))
<BR><BR>[Stack]<UL><LI>Max Depth = 8<LI>Call Chain = USART_ClearITPendingBit
</UL>
<BR>[Called By]<UL><LI><a href="#[55]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;USART6_IRQHandler
</UL>

<P><STRONG><a name="[db]"></a>USART_Cmd</STRONG> (Thumb, 24 bytes, Stack size 0 bytes, stm32f4xx_usart.o(i.USART_Cmd))
<BR><BR>[Called By]<UL><LI><a href="#[d7]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Init_Uart
</UL>

<P><STRONG><a name="[15a]"></a>USART_GetFlagStatus</STRONG> (Thumb, 26 bytes, Stack size 0 bytes, stm32f4xx_usart.o(i.USART_GetFlagStatus))
<BR><BR>[Called By]<UL><LI><a href="#[158]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;usartSendData
</UL>

<P><STRONG><a name="[139]"></a>USART_GetITStatus</STRONG> (Thumb, 84 bytes, Stack size 16 bytes, stm32f4xx_usart.o(i.USART_GetITStatus))
<BR><BR>[Stack]<UL><LI>Max Depth = 16<LI>Call Chain = USART_GetITStatus
</UL>
<BR>[Called By]<UL><LI><a href="#[33]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;USART1_IRQHandler
<LI><a href="#[55]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;USART6_IRQHandler
</UL>

<P><STRONG><a name="[da]"></a>USART_Init</STRONG> (Thumb, 204 bytes, Stack size 48 bytes, stm32f4xx_usart.o(i.USART_Init))
<BR><BR>[Stack]<UL><LI>Max Depth = 68<LI>Call Chain = USART_Init &rArr; RCC_GetClocksFreq
</UL>
<BR>[Calls]<UL><LI><a href="#[13c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;RCC_GetClocksFreq
</UL>
<BR>[Called By]<UL><LI><a href="#[d7]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Init_Uart
</UL>

<P><STRONG><a name="[13a]"></a>USART_ReceiveData</STRONG> (Thumb, 10 bytes, Stack size 0 bytes, stm32f4xx_usart.o(i.USART_ReceiveData))
<BR><BR>[Called By]<UL><LI><a href="#[33]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;USART1_IRQHandler
<LI><a href="#[55]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;USART6_IRQHandler
</UL>

<P><STRONG><a name="[159]"></a>USART_SendData</STRONG> (Thumb, 8 bytes, Stack size 0 bytes, stm32f4xx_usart.o(i.USART_SendData))
<BR><BR>[Called By]<UL><LI><a href="#[158]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;usartSendData
</UL>

<P><STRONG><a name="[9]"></a>UsageFault_Handler</STRONG> (Thumb, 4 bytes, Stack size 0 bytes, stm32f4xx_it.o(i.UsageFault_Handler))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f40_41xxx.o(RESET)
</UL>
<P><STRONG><a name="[13d]"></a>User_ADC_GPIO_Init</STRONG> (Thumb, 58 bytes, Stack size 16 bytes, user_adc.o(i.User_ADC_GPIO_Init))
<BR><BR>[Stack]<UL><LI>Max Depth = 36<LI>Call Chain = User_ADC_GPIO_Init &rArr; GPIO_Init
</UL>
<BR>[Calls]<UL><LI><a href="#[c6]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;RCC_AHB1PeriphClockCmd
<LI><a href="#[c7]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;GPIO_Init
</UL>
<BR>[Called By]<UL><LI><a href="#[ce]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;User_ADC_Init
</UL>

<P><STRONG><a name="[ce]"></a>User_ADC_Init</STRONG> (Thumb, 70 bytes, Stack size 8 bytes, user_adc.o(i.User_ADC_Init))
<BR><BR>[Stack]<UL><LI>Max Depth = 88<LI>Call Chain = User_ADC_Init &rArr; IIR_ADC_DMA_Init &rArr; DMA_Init
</UL>
<BR>[Calls]<UL><LI><a href="#[13d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;User_ADC_GPIO_Init
<LI><a href="#[c8]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;IIR_ADC_DMA_Init
<LI><a href="#[b3]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ADC_TIM3_Init
<LI><a href="#[b2]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ADC_Mode_Independent_Init
<LI><a href="#[aa]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ADC_DualMode_RegSimult_Init
<LI><a href="#[a7]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ADC_DMA_NVIC_Init
</UL>
<BR>[Called By]<UL><LI><a href="#[d3]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Init_All
<LI><a href="#[cd]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;IIR_Init
</UL>

<P><STRONG><a name="[13e]"></a>User_DAC_Configure</STRONG> (Thumb, 46 bytes, Stack size 24 bytes, user_dac.o(i.User_DAC_Configure))
<BR><BR>[Stack]<UL><LI>Max Depth = 36<LI>Call Chain = User_DAC_Configure &rArr; DAC_Init
</UL>
<BR>[Calls]<UL><LI><a href="#[b4]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;RCC_APB1PeriphClockCmd
<LI><a href="#[140]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;DAC_Init
<LI><a href="#[141]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;DAC_DMACmd
<LI><a href="#[142]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;DAC_Cmd
<LI><a href="#[13f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;DAC_StructInit
<LI><a href="#[bd]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;DAC_DeInit
</UL>
<BR>[Called By]<UL><LI><a href="#[d5]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;User_DAC_Init
</UL>

<P><STRONG><a name="[143]"></a>User_DAC_DMA_Init</STRONG> (Thumb, 100 bytes, Stack size 72 bytes, user_dac.o(i.User_DAC_DMA_Init))
<BR><BR>[Stack]<UL><LI>Max Depth = 80<LI>Call Chain = User_DAC_DMA_Init &rArr; DMA_Init
</UL>
<BR>[Calls]<UL><LI><a href="#[c6]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;RCC_AHB1PeriphClockCmd
<LI><a href="#[ca]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;DMA_Init
<LI><a href="#[c9]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;DMA_DeInit
<LI><a href="#[cc]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;DMA_Cmd
</UL>
<BR>[Called By]<UL><LI><a href="#[d5]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;User_DAC_Init
</UL>

<P><STRONG><a name="[144]"></a>User_DAC_GPIO_Init</STRONG> (Thumb, 58 bytes, Stack size 16 bytes, user_dac.o(i.User_DAC_GPIO_Init))
<BR><BR>[Stack]<UL><LI>Max Depth = 36<LI>Call Chain = User_DAC_GPIO_Init &rArr; GPIO_Init
</UL>
<BR>[Calls]<UL><LI><a href="#[c6]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;RCC_AHB1PeriphClockCmd
<LI><a href="#[c7]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;GPIO_Init
</UL>
<BR>[Called By]<UL><LI><a href="#[d5]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;User_DAC_Init
</UL>

<P><STRONG><a name="[d5]"></a>User_DAC_Init</STRONG> (Thumb, 34 bytes, Stack size 8 bytes, user_dac.o(i.User_DAC_Init))
<BR><BR>[Stack]<UL><LI>Max Depth = 88<LI>Call Chain = User_DAC_Init &rArr; User_DAC_DMA_Init &rArr; DMA_Init
</UL>
<BR>[Calls]<UL><LI><a href="#[f2]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;DAC_SetChannel1Data
<LI><a href="#[145]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;User_DAC_TIM_Init
<LI><a href="#[144]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;User_DAC_GPIO_Init
<LI><a href="#[143]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;User_DAC_DMA_Init
<LI><a href="#[13e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;User_DAC_Configure
</UL>
<BR>[Called By]<UL><LI><a href="#[d3]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Init_All
</UL>

<P><STRONG><a name="[145]"></a>User_DAC_TIM_Init</STRONG> (Thumb, 90 bytes, Stack size 16 bytes, user_dac.o(i.User_DAC_TIM_Init))
<BR><BR>[Stack]<UL><LI>Max Depth = 40<LI>Call Chain = User_DAC_TIM_Init &rArr; Set_TriggerFre
</UL>
<BR>[Calls]<UL><LI><a href="#[b7]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;TIM_UpdateDisableConfig
<LI><a href="#[b5]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;TIM_TimeBaseInit
<LI><a href="#[b6]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;TIM_SelectOutputTrigger
<LI><a href="#[b8]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;TIM_Cmd
<LI><a href="#[b4]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;RCC_APB1PeriphClockCmd
<LI><a href="#[146]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;TIM_DMACmd
<LI><a href="#[135]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Set_TriggerFre
</UL>
<BR>[Called By]<UL><LI><a href="#[d5]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;User_DAC_Init
</UL>

<P><STRONG><a name="[123]"></a>User_SPI_SendData</STRONG> (Thumb, 180 bytes, Stack size 24 bytes, user_spi.o(i.User_SPI_SendData))
<BR><BR>[Stack]<UL><LI>Max Depth = 64 + Unknown Stack Size
<LI>Call Chain = User_SPI_SendData &rArr; delay_us &rArr; OS_SchedUnlock
</UL>
<BR>[Calls]<UL><LI><a href="#[12b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;GPIO_ResetBits
<LI><a href="#[147]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;delay_us
<LI><a href="#[12a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;GPIO_SetBits
</UL>
<BR>[Called By]<UL><LI><a href="#[120]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;PGA2310_SetAv
</UL>

<P><STRONG><a name="[66]"></a>User_main</STRONG> (Thumb, 102 bytes, Stack size 0 bytes, user.o(i.User_main))
<BR><BR>[Stack]<UL><LI>Max Depth = 632 + Unknown Stack Size
<LI>Call Chain = User_main &rArr; MenuHaddler_1 &rArr; PS2_ReadNum &rArr; OS_Num_Show &rArr; OS_String_Show &rArr; OS_Font_Show &rArr; OS_HzMat_Get &rArr; W25Q64_Read &rArr; SPI1_ReadWriteByte
</UL>
<BR>[Calls]<UL><LI><a href="#[a6]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;delay_ms
<LI><a href="#[148]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;menu_show
<LI><a href="#[100]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;My_Disp_Main
<LI><a href="#[ff]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;MenuHaddler_4
<LI><a href="#[fd]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;MenuHaddler_3
<LI><a href="#[fc]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;MenuHaddler_2
<LI><a href="#[f0]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;MenuHaddler_1
<LI><a href="#[d3]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Init_All
<LI><a href="#[ba]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Change_Menu
</UL>
<BR>[Address Reference Count : 1]<UL><LI> main.o(i.main)
</UL>
<P><STRONG><a name="[149]"></a>W25Q64_Init</STRONG> (Thumb, 106 bytes, Stack size 16 bytes, w25q64.o(i.W25Q64_Init))
<BR><BR>[Stack]<UL><LI>Max Depth = 68<LI>Call Chain = W25Q64_Init &rArr; SPI1_Init &rArr; GPIO_Init
</UL>
<BR>[Calls]<UL><LI><a href="#[c6]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;RCC_AHB1PeriphClockCmd
<LI><a href="#[c7]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;GPIO_Init
<LI><a href="#[12a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;GPIO_SetBits
<LI><a href="#[14a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;W25Q64_ReadID
<LI><a href="#[134]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SPI1_SetSpeed
<LI><a href="#[12c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SPI1_Init
</UL>
<BR>[Called By]<UL><LI><a href="#[10f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;font_init
</UL>

<P><STRONG><a name="[109]"></a>W25Q64_Read</STRONG> (Thumb, 82 bytes, Stack size 24 bytes, w25q64.o(i.W25Q64_Read))
<BR><BR>[Stack]<UL><LI>Max Depth = 32<LI>Call Chain = W25Q64_Read &rArr; SPI1_ReadWriteByte
</UL>
<BR>[Calls]<UL><LI><a href="#[12b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;GPIO_ResetBits
<LI><a href="#[12a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;GPIO_SetBits
<LI><a href="#[130]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SPI1_ReadWriteByte
</UL>
<BR>[Called By]<UL><LI><a href="#[10f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;font_init
<LI><a href="#[108]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;OS_HzMat_Get
</UL>

<P><STRONG><a name="[14a]"></a>W25Q64_ReadID</STRONG> (Thumb, 70 bytes, Stack size 8 bytes, w25q64.o(i.W25Q64_ReadID))
<BR><BR>[Stack]<UL><LI>Max Depth = 16<LI>Call Chain = W25Q64_ReadID &rArr; SPI1_ReadWriteByte
</UL>
<BR>[Calls]<UL><LI><a href="#[12b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;GPIO_ResetBits
<LI><a href="#[12a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;GPIO_SetBits
<LI><a href="#[130]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SPI1_ReadWriteByte
</UL>
<BR>[Called By]<UL><LI><a href="#[149]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;W25Q64_Init
</UL>

<P><STRONG><a name="[96]"></a>__ARM_fpclassify</STRONG> (Thumb, 48 bytes, Stack size 8 bytes, fpclassify.o(i.__ARM_fpclassify))
<BR><BR>[Stack]<UL><LI>Max Depth = 8<LI>Call Chain = __ARM_fpclassify
</UL>
<BR>[Called By]<UL><LI><a href="#[129]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__hardfp_pow
<LI><a href="#[9a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_printf_fp_hex_real
<LI><a href="#[95]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_printf_fp_dec_real
</UL>

<P><STRONG><a name="[129]"></a>__hardfp_pow</STRONG> (Thumb, 3072 bytes, Stack size 192 bytes, pow.o(i.__hardfp_pow))
<BR><BR>[Stack]<UL><LI>Max Depth = 264<LI>Call Chain = __hardfp_pow &rArr; sqrt &rArr; _dsqrt &rArr; __fpl_dnaninf
</UL>
<BR>[Calls]<UL><LI><a href="#[121]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_cdrcmple
<LI><a href="#[f5]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_dmul
<LI><a href="#[151]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_i2d
<LI><a href="#[f9]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_ddiv
<LI><a href="#[fa]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_dsub
<LI><a href="#[f6]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_drsub
<LI><a href="#[fb]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_dadd
<LI><a href="#[14d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_dneg
<LI><a href="#[154]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__kernel_poly
<LI><a href="#[96]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__ARM_fpclassify
<LI><a href="#[150]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;fabs
<LI><a href="#[153]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__mathlib_dbl_underflow
<LI><a href="#[14e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__mathlib_dbl_overflow
<LI><a href="#[152]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__mathlib_dbl_invalid
<LI><a href="#[14b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__mathlib_dbl_infnan2
<LI><a href="#[14c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__mathlib_dbl_divzero
<LI><a href="#[155]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__ARM_scalbn
<LI><a href="#[8e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__set_errno
<LI><a href="#[14f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;sqrt
</UL>
<BR>[Called By]<UL><LI><a href="#[f3]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;PS2_ReadNum
</UL>

<P><STRONG><a name="[f8]"></a>__hardfp_sqrt</STRONG> (Thumb, 122 bytes, Stack size 32 bytes, sqrt.o(i.__hardfp_sqrt))
<BR><BR>[Stack]<UL><LI>Max Depth = 72<LI>Call Chain = __hardfp_sqrt &rArr; _dsqrt &rArr; __fpl_dnaninf
</UL>
<BR>[Calls]<UL><LI><a href="#[156]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_dsqrt
<LI><a href="#[8e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__set_errno
</UL>
<BR>[Called By]<UL><LI><a href="#[f0]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;MenuHaddler_1
</UL>

<P><STRONG><a name="[154]"></a>__kernel_poly</STRONG> (Thumb, 248 bytes, Stack size 24 bytes, poly.o(i.__kernel_poly))
<BR><BR>[Stack]<UL><LI>Max Depth = 56<LI>Call Chain = __kernel_poly &rArr; __aeabi_dmul
</UL>
<BR>[Calls]<UL><LI><a href="#[f5]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_dmul
<LI><a href="#[fb]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_dadd
</UL>
<BR>[Called By]<UL><LI><a href="#[129]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__hardfp_pow
</UL>

<P><STRONG><a name="[14c]"></a>__mathlib_dbl_divzero</STRONG> (Thumb, 28 bytes, Stack size 8 bytes, dunder.o(i.__mathlib_dbl_divzero))
<BR><BR>[Stack]<UL><LI>Max Depth = 40<LI>Call Chain = __mathlib_dbl_divzero &rArr; __aeabi_ddiv
</UL>
<BR>[Calls]<UL><LI><a href="#[f9]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_ddiv
</UL>
<BR>[Called By]<UL><LI><a href="#[129]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__hardfp_pow
</UL>

<P><STRONG><a name="[14b]"></a>__mathlib_dbl_infnan2</STRONG> (Thumb, 20 bytes, Stack size 8 bytes, dunder.o(i.__mathlib_dbl_infnan2))
<BR><BR>[Stack]<UL><LI>Max Depth = 24<LI>Call Chain = __mathlib_dbl_infnan2 &rArr; __aeabi_dadd
</UL>
<BR>[Calls]<UL><LI><a href="#[fb]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_dadd
</UL>
<BR>[Called By]<UL><LI><a href="#[129]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__hardfp_pow
</UL>

<P><STRONG><a name="[152]"></a>__mathlib_dbl_invalid</STRONG> (Thumb, 24 bytes, Stack size 8 bytes, dunder.o(i.__mathlib_dbl_invalid))
<BR><BR>[Stack]<UL><LI>Max Depth = 40<LI>Call Chain = __mathlib_dbl_invalid &rArr; __aeabi_ddiv
</UL>
<BR>[Calls]<UL><LI><a href="#[f9]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_ddiv
</UL>
<BR>[Called By]<UL><LI><a href="#[129]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__hardfp_pow
</UL>

<P><STRONG><a name="[14e]"></a>__mathlib_dbl_overflow</STRONG> (Thumb, 24 bytes, Stack size 8 bytes, dunder.o(i.__mathlib_dbl_overflow))
<BR><BR>[Stack]<UL><LI>Max Depth = 40<LI>Call Chain = __mathlib_dbl_overflow &rArr; __aeabi_dmul
</UL>
<BR>[Calls]<UL><LI><a href="#[f5]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_dmul
</UL>
<BR>[Called By]<UL><LI><a href="#[129]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__hardfp_pow
</UL>

<P><STRONG><a name="[153]"></a>__mathlib_dbl_underflow</STRONG> (Thumb, 24 bytes, Stack size 8 bytes, dunder.o(i.__mathlib_dbl_underflow))
<BR><BR>[Stack]<UL><LI>Max Depth = 40<LI>Call Chain = __mathlib_dbl_underflow &rArr; __aeabi_dmul
</UL>
<BR>[Calls]<UL><LI><a href="#[f5]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_dmul
</UL>
<BR>[Called By]<UL><LI><a href="#[129]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__hardfp_pow
</UL>

<P><STRONG><a name="[8a]"></a>_is_digit</STRONG> (Thumb, 14 bytes, Stack size 0 bytes, __printf_wp.o(i._is_digit))
<BR><BR>[Called By]<UL><LI><a href="#[89]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__printf
</UL>

<P><STRONG><a name="[84]"></a>_sys_exit</STRONG> (Thumb, 6 bytes, Stack size 0 bytes, usart.o(i._sys_exit))
<BR><BR>[Called By]<UL><LI><a href="#[83]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__rt_exit_exit
</UL>

<P><STRONG><a name="[a6]"></a>delay_ms</STRONG> (Thumb, 60 bytes, Stack size 16 bytes, delay.o(i.delay_ms))
<BR><BR>[Stack]<UL><LI>Max Depth = 56 + Unknown Stack Size
<LI>Call Chain = delay_ms &rArr; delay_us &rArr; OS_SchedUnlock
</UL>
<BR>[Calls]<UL><LI><a href="#[ef]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;OSTimeDly
<LI><a href="#[147]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;delay_us
</UL>
<BR>[Called By]<UL><LI><a href="#[67]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;MyPs2KeyScan
<LI><a href="#[bf]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;DDSDataInit
<LI><a href="#[66]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;User_main
<LI><a href="#[fd]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;MenuHaddler_3
<LI><a href="#[fc]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;MenuHaddler_2
<LI><a href="#[f0]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;MenuHaddler_1
<LI><a href="#[a3]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;AD9959_senddata
<LI><a href="#[10f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;font_init
<LI><a href="#[10e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;TFT_LCD_Init
</UL>

<P><STRONG><a name="[147]"></a>delay_us</STRONG> (Thumb, 92 bytes, Stack size 32 bytes, delay.o(i.delay_us))
<BR><BR>[Stack]<UL><LI>Max Depth = 40 + Unknown Stack Size
<LI>Call Chain = delay_us &rArr; OS_SchedUnlock
</UL>
<BR>[Calls]<UL><LI><a href="#[116]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;OS_SchedUnlock
<LI><a href="#[115]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;OS_SchedLock
</UL>
<BR>[Called By]<UL><LI><a href="#[a6]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;delay_ms
<LI><a href="#[123]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;User_SPI_SendData
</UL>

<P><STRONG><a name="[150]"></a>fabs</STRONG> (Thumb, 24 bytes, Stack size 8 bytes, fabs.o(i.fabs))
<BR><BR>[Stack]<UL><LI>Max Depth = 8<LI>Call Chain = fabs
</UL>
<BR>[Called By]<UL><LI><a href="#[129]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__hardfp_pow
</UL>

<P><STRONG><a name="[10f]"></a>font_init</STRONG> (Thumb, 64 bytes, Stack size 8 bytes, fontupd.o(i.font_init))
<BR><BR>[Stack]<UL><LI>Max Depth = 76 + Unknown Stack Size
<LI>Call Chain = font_init &rArr; W25Q64_Init &rArr; SPI1_Init &rArr; GPIO_Init
</UL>
<BR>[Calls]<UL><LI><a href="#[a6]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;delay_ms
<LI><a href="#[109]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;W25Q64_Read
<LI><a href="#[149]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;W25Q64_Init
</UL>
<BR>[Called By]<UL><LI><a href="#[10d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;OS_LCD_Init
</UL>

<P><STRONG><a name="[d1]"></a>init_iir_filter</STRONG> (Thumb, 84 bytes, Stack size 8 bytes, user_adc.o(i.init_iir_filter))
<BR><BR>[Stack]<UL><LI>Max Depth = 28<LI>Call Chain = init_iir_filter &rArr; arm_biquad_cascade_df1_init_f32 &rArr; __aeabi_memclr4
</UL>
<BR>[Calls]<UL><LI><a href="#[85]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;arm_biquad_cascade_df1_init_f32
</UL>
<BR>[Called By]<UL><LI><a href="#[cd]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;IIR_Init
</UL>

<P><STRONG><a name="[7f]"></a>main</STRONG> (Thumb, 44 bytes, Stack size 8 bytes, main.o(i.main))
<BR><BR>[Stack]<UL><LI>Max Depth = 336 + Unknown Stack Size
<LI>Call Chain = main &rArr; OS_Init &rArr; OS_LCD_Init &rArr; OS_String_Show &rArr; OS_Font_Show &rArr; OS_HzMat_Get &rArr; W25Q64_Read &rArr; SPI1_ReadWriteByte
</UL>
<BR>[Calls]<UL><LI><a href="#[118]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Task_Create
<LI><a href="#[117]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;OS_Start
<LI><a href="#[10a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;OS_Init
</UL>
<BR>[Called By]<UL><LI><a href="#[7e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__rt_entry_main
</UL>

<P><STRONG><a name="[148]"></a>menu_show</STRONG> (Thumb, 56 bytes, Stack size 8 bytes, user.o(i.menu_show))
<BR><BR>[Stack]<UL><LI>Max Depth = 336<LI>Call Chain = menu_show &rArr; My_Disp_Main &rArr; OS_String_Show &rArr; OS_Font_Show &rArr; OS_HzMat_Get &rArr; W25Q64_Read &rArr; SPI1_ReadWriteByte
</UL>
<BR>[Calls]<UL><LI><a href="#[bb]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;LCD_Appoint_Clear
<LI><a href="#[100]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;My_Disp_Main
</UL>
<BR>[Called By]<UL><LI><a href="#[66]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;User_main
</UL>

<P><STRONG><a name="[a5]"></a>sendData</STRONG> (Thumb, 388 bytes, Stack size 40 bytes, drive_communication.o(i.sendData))
<BR><BR>[Stack]<UL><LI>Max Depth = 56<LI>Call Chain = sendData &rArr; usartSendData
</UL>
<BR>[Calls]<UL><LI><a href="#[158]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;usartSendData
<LI><a href="#[157]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;crc_16
</UL>
<BR>[Called By]<UL><LI><a href="#[bf]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;DDSDataInit
<LI><a href="#[a3]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;AD9959_senddata
</UL>

<P><STRONG><a name="[14f]"></a>sqrt</STRONG> (Thumb, 110 bytes, Stack size 32 bytes, sqrt.o(i.sqrt))
<BR><BR>[Stack]<UL><LI>Max Depth = 72<LI>Call Chain = sqrt &rArr; _dsqrt &rArr; __fpl_dnaninf
</UL>
<BR>[Calls]<UL><LI><a href="#[156]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_dsqrt
<LI><a href="#[8e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__set_errno
</UL>
<BR>[Called By]<UL><LI><a href="#[129]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__hardfp_pow
</UL>

<P><STRONG><a name="[79]"></a>_get_lc_numeric</STRONG> (Thumb, 44 bytes, Stack size 8 bytes, lc_numeric_c.o(locale$$code))
<BR><BR>[Stack]<UL><LI>Max Depth = 8<LI>Call Chain = _get_lc_numeric
</UL>
<BR>[Calls]<UL><LI><a href="#[15b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;strcmp
</UL>
<BR>[Called By]<UL><LI><a href="#[78]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__rt_lib_init_lc_numeric_2
</UL>

<P><STRONG><a name="[14d]"></a>__aeabi_dneg</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, basic.o(x$fpl$basic))
<BR><BR>[Called By]<UL><LI><a href="#[129]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__hardfp_pow
</UL>

<P><STRONG><a name="[1a5]"></a>_dneg</STRONG> (Thumb, 6 bytes, Stack size 0 bytes, basic.o(x$fpl$basic), UNUSED)

<P><STRONG><a name="[1a6]"></a>__aeabi_fneg</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, basic.o(x$fpl$basic), UNUSED)

<P><STRONG><a name="[1a7]"></a>_fneg</STRONG> (Thumb, 6 bytes, Stack size 0 bytes, basic.o(x$fpl$basic), UNUSED)

<P><STRONG><a name="[1a8]"></a>_dabs</STRONG> (Thumb, 6 bytes, Stack size 0 bytes, basic.o(x$fpl$basic), UNUSED)

<P><STRONG><a name="[1a9]"></a>_fabs</STRONG> (Thumb, 6 bytes, Stack size 0 bytes, basic.o(x$fpl$basic), UNUSED)

<P><STRONG><a name="[f7]"></a>__aeabi_d2f</STRONG> (Thumb, 0 bytes, Stack size 32 bytes, d2f.o(x$fpl$d2f))
<BR><BR>[Stack]<UL><LI>Max Depth = 32<LI>Call Chain = __aeabi_d2f
</UL>
<BR>[Called By]<UL><LI><a href="#[f3]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;PS2_ReadNum
<LI><a href="#[f0]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;MenuHaddler_1
</UL>

<P><STRONG><a name="[15c]"></a>_d2f</STRONG> (Thumb, 98 bytes, Stack size 32 bytes, d2f.o(x$fpl$d2f), UNUSED)
<BR><BR>[Calls]<UL><LI><a href="#[15d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__fpl_fretinf
<LI><a href="#[15e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__fpl_dnaninf
</UL>

<P><STRONG><a name="[fb]"></a>__aeabi_dadd</STRONG> (Thumb, 0 bytes, Stack size 16 bytes, daddsub_clz.o(x$fpl$dadd))
<BR><BR>[Stack]<UL><LI>Max Depth = 16<LI>Call Chain = __aeabi_dadd
</UL>
<BR>[Called By]<UL><LI><a href="#[f3]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;PS2_ReadNum
<LI><a href="#[f0]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;MenuHaddler_1
<LI><a href="#[129]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__hardfp_pow
<LI><a href="#[154]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__kernel_poly
<LI><a href="#[14b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__mathlib_dbl_infnan2
</UL>

<P><STRONG><a name="[15f]"></a>_dadd</STRONG> (Thumb, 332 bytes, Stack size 16 bytes, daddsub_clz.o(x$fpl$dadd), UNUSED)
<BR><BR>[Calls]<UL><LI><a href="#[160]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_dsub1
<LI><a href="#[161]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__fpl_dretinf
<LI><a href="#[15e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__fpl_dnaninf
</UL>

<P><STRONG><a name="[162]"></a>__fpl_dcheck_NaN1</STRONG> (Thumb, 10 bytes, Stack size 0 bytes, dcheck1.o(x$fpl$dcheck1))
<BR><BR>[Calls]<UL><LI><a href="#[163]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__fpl_return_NaN
</UL>
<BR>[Called By]<UL><LI><a href="#[155]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__ARM_scalbn
</UL>

<P><STRONG><a name="[166]"></a>__fpl_dcmp_Inf</STRONG> (Thumb, 24 bytes, Stack size 0 bytes, dcmpi.o(x$fpl$dcmpinf), UNUSED)
<BR><BR>[Called By]<UL><LI><a href="#[165]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_dcmple
</UL>

<P><STRONG><a name="[f9]"></a>__aeabi_ddiv</STRONG> (Thumb, 0 bytes, Stack size 32 bytes, ddiv.o(x$fpl$ddiv))
<BR><BR>[Stack]<UL><LI>Max Depth = 32<LI>Call Chain = __aeabi_ddiv
</UL>
<BR>[Called By]<UL><LI><a href="#[f3]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;PS2_ReadNum
<LI><a href="#[f0]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;MenuHaddler_1
<LI><a href="#[129]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__hardfp_pow
<LI><a href="#[152]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__mathlib_dbl_invalid
<LI><a href="#[14c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__mathlib_dbl_divzero
</UL>

<P><STRONG><a name="[164]"></a>_ddiv</STRONG> (Thumb, 552 bytes, Stack size 32 bytes, ddiv.o(x$fpl$ddiv), UNUSED)
<BR><BR>[Calls]<UL><LI><a href="#[161]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__fpl_dretinf
<LI><a href="#[15e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__fpl_dnaninf
</UL>

<P><STRONG><a name="[151]"></a>__aeabi_i2d</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, dflt_clz.o(x$fpl$dflt))
<BR><BR>[Called By]<UL><LI><a href="#[129]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__hardfp_pow
</UL>

<P><STRONG><a name="[1aa]"></a>_dflt</STRONG> (Thumb, 46 bytes, Stack size 0 bytes, dflt_clz.o(x$fpl$dflt), UNUSED)

<P><STRONG><a name="[128]"></a>__aeabi_ui2d</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, dflt_clz.o(x$fpl$dfltu))
<BR><BR>[Called By]<UL><LI><a href="#[f3]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;PS2_ReadNum
</UL>

<P><STRONG><a name="[1ab]"></a>_dfltu</STRONG> (Thumb, 38 bytes, Stack size 0 bytes, dflt_clz.o(x$fpl$dfltu), UNUSED)

<P><STRONG><a name="[122]"></a>__aeabi_cdcmple</STRONG> (Thumb, 0 bytes, Stack size 32 bytes, dleqf.o(x$fpl$dleqf))
<BR><BR>[Stack]<UL><LI>Max Depth = 32<LI>Call Chain = __aeabi_cdcmple
</UL>
<BR>[Called By]<UL><LI><a href="#[120]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;PGA2310_SetAv
</UL>

<P><STRONG><a name="[165]"></a>_dcmple</STRONG> (Thumb, 120 bytes, Stack size 32 bytes, dleqf.o(x$fpl$dleqf), UNUSED)
<BR><BR>[Calls]<UL><LI><a href="#[15e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__fpl_dnaninf
<LI><a href="#[166]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__fpl_dcmp_Inf
</UL>

<P><STRONG><a name="[169]"></a>__fpl_dcmple_InfNaN</STRONG> (Thumb, 0 bytes, Stack size unknown bytes, dleqf.o(x$fpl$dleqf), UNUSED)
<BR><BR>[Called By]<UL><LI><a href="#[168]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_drcmple
</UL>

<P><STRONG><a name="[f5]"></a>__aeabi_dmul</STRONG> (Thumb, 0 bytes, Stack size 32 bytes, dmul.o(x$fpl$dmul))
<BR><BR>[Stack]<UL><LI>Max Depth = 32<LI>Call Chain = __aeabi_dmul
</UL>
<BR>[Called By]<UL><LI><a href="#[f0]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;MenuHaddler_1
<LI><a href="#[129]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__hardfp_pow
<LI><a href="#[154]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__kernel_poly
<LI><a href="#[153]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__mathlib_dbl_underflow
<LI><a href="#[14e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__mathlib_dbl_overflow
</UL>

<P><STRONG><a name="[167]"></a>_dmul</STRONG> (Thumb, 332 bytes, Stack size 32 bytes, dmul.o(x$fpl$dmul), UNUSED)
<BR><BR>[Calls]<UL><LI><a href="#[161]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__fpl_dretinf
<LI><a href="#[15e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__fpl_dnaninf
</UL>

<P><STRONG><a name="[15e]"></a>__fpl_dnaninf</STRONG> (Thumb, 156 bytes, Stack size 16 bytes, dnaninf.o(x$fpl$dnaninf))
<BR><BR>[Stack]<UL><LI>Max Depth = 16<LI>Call Chain = __fpl_dnaninf
</UL>
<BR>[Called By]<UL><LI><a href="#[167]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_dmul
<LI><a href="#[165]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_dcmple
<LI><a href="#[164]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_ddiv
<LI><a href="#[16c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_dsub
<LI><a href="#[15f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_dadd
<LI><a href="#[15c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_d2f
<LI><a href="#[156]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_dsqrt
</UL>

<P><STRONG><a name="[161]"></a>__fpl_dretinf</STRONG> (Thumb, 12 bytes, Stack size 0 bytes, dretinf.o(x$fpl$dretinf), UNUSED)
<BR><BR>[Called By]<UL><LI><a href="#[16d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_f2d
<LI><a href="#[167]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_dmul
<LI><a href="#[164]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_ddiv
<LI><a href="#[15f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_dadd
</UL>

<P><STRONG><a name="[121]"></a>__aeabi_cdrcmple</STRONG> (Thumb, 0 bytes, Stack size 16 bytes, drleqf.o(x$fpl$drleqf))
<BR><BR>[Stack]<UL><LI>Max Depth = 16<LI>Call Chain = __aeabi_cdrcmple
</UL>
<BR>[Called By]<UL><LI><a href="#[120]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;PGA2310_SetAv
<LI><a href="#[129]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__hardfp_pow
</UL>

<P><STRONG><a name="[168]"></a>_drcmple</STRONG> (Thumb, 108 bytes, Stack size 16 bytes, drleqf.o(x$fpl$drleqf), UNUSED)
<BR><BR>[Calls]<UL><LI><a href="#[169]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__fpl_dcmple_InfNaN
</UL>

<P><STRONG><a name="[f6]"></a>__aeabi_drsub</STRONG> (Thumb, 0 bytes, Stack size 8 bytes, daddsub_clz.o(x$fpl$drsb))
<BR><BR>[Stack]<UL><LI>Max Depth = 8<LI>Call Chain = __aeabi_drsub
</UL>
<BR>[Called By]<UL><LI><a href="#[f0]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;MenuHaddler_1
<LI><a href="#[129]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__hardfp_pow
</UL>

<P><STRONG><a name="[16a]"></a>_drsb</STRONG> (Thumb, 22 bytes, Stack size 8 bytes, daddsub_clz.o(x$fpl$drsb), UNUSED)
<BR><BR>[Calls]<UL><LI><a href="#[160]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_dsub1
<LI><a href="#[16b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_dadd1
</UL>

<P><STRONG><a name="[156]"></a>_dsqrt</STRONG> (Thumb, 404 bytes, Stack size 24 bytes, dsqrt_umaal.o(x$fpl$dsqrt))
<BR><BR>[Stack]<UL><LI>Max Depth = 40<LI>Call Chain = _dsqrt &rArr; __fpl_dnaninf
</UL>
<BR>[Calls]<UL><LI><a href="#[15e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__fpl_dnaninf
</UL>
<BR>[Called By]<UL><LI><a href="#[14f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;sqrt
<LI><a href="#[f8]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__hardfp_sqrt
</UL>

<P><STRONG><a name="[fa]"></a>__aeabi_dsub</STRONG> (Thumb, 0 bytes, Stack size 32 bytes, daddsub_clz.o(x$fpl$dsub))
<BR><BR>[Stack]<UL><LI>Max Depth = 32<LI>Call Chain = __aeabi_dsub
</UL>
<BR>[Called By]<UL><LI><a href="#[f0]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;MenuHaddler_1
<LI><a href="#[129]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__hardfp_pow
</UL>

<P><STRONG><a name="[16c]"></a>_dsub</STRONG> (Thumb, 464 bytes, Stack size 32 bytes, daddsub_clz.o(x$fpl$dsub), UNUSED)
<BR><BR>[Calls]<UL><LI><a href="#[16b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_dadd1
<LI><a href="#[15e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__fpl_dnaninf
</UL>

<P><STRONG><a name="[f4]"></a>__aeabi_f2d</STRONG> (Thumb, 0 bytes, Stack size 16 bytes, f2d.o(x$fpl$f2d))
<BR><BR>[Stack]<UL><LI>Max Depth = 16<LI>Call Chain = __aeabi_f2d
</UL>
<BR>[Called By]<UL><LI><a href="#[f1]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Show_Val
<LI><a href="#[f3]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;PS2_ReadNum
<LI><a href="#[f0]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;MenuHaddler_1
<LI><a href="#[120]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;PGA2310_SetAv
</UL>

<P><STRONG><a name="[16d]"></a>_f2d</STRONG> (Thumb, 86 bytes, Stack size 16 bytes, f2d.o(x$fpl$f2d), UNUSED)
<BR><BR>[Calls]<UL><LI><a href="#[16e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__fpl_fnaninf
<LI><a href="#[161]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__fpl_dretinf
</UL>

<P><STRONG><a name="[16e]"></a>__fpl_fnaninf</STRONG> (Thumb, 140 bytes, Stack size 8 bytes, fnaninf.o(x$fpl$fnaninf), UNUSED)
<BR><BR>[Called By]<UL><LI><a href="#[16d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_f2d
</UL>

<P><STRONG><a name="[75]"></a>_fp_init</STRONG> (Thumb, 10 bytes, Stack size 0 bytes, fpinit.o(x$fpl$fpinit))
<BR><BR>[Called By]<UL><LI><a href="#[74]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__rt_lib_init_fp_1
</UL>

<P><STRONG><a name="[1ac]"></a>__fplib_config_fpu_vfp</STRONG> (Thumb, 0 bytes, Stack size unknown bytes, fpinit.o(x$fpl$fpinit), UNUSED)

<P><STRONG><a name="[1ad]"></a>__fplib_config_pureend_doubles</STRONG> (Thumb, 0 bytes, Stack size unknown bytes, fpinit.o(x$fpl$fpinit), UNUSED)

<P><STRONG><a name="[15d]"></a>__fpl_fretinf</STRONG> (Thumb, 10 bytes, Stack size 0 bytes, fretinf.o(x$fpl$fretinf), UNUSED)
<BR><BR>[Called By]<UL><LI><a href="#[15c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_d2f
</UL>

<P><STRONG><a name="[6f]"></a>_printf_fp_dec</STRONG> (Thumb, 4 bytes, Stack size 0 bytes, printf1.o(x$fpl$printf1))
<BR><BR>[Stack]<UL><LI>Max Depth = 324<LI>Call Chain = _printf_fp_dec &rArr; _printf_fp_dec_real &rArr; _fp_digits &rArr; _btod_etento &rArr; _btod_emul &rArr; _e2e
</UL>
<BR>[Calls]<UL><LI><a href="#[95]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_printf_fp_dec_real
</UL>
<BR>[Called By]<UL><LI><a href="#[71]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_printf_g
<LI><a href="#[70]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_printf_e
<LI><a href="#[6e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_printf_f
</UL>

<P><STRONG><a name="[73]"></a>_printf_fp_hex</STRONG> (Thumb, 4 bytes, Stack size 0 bytes, printf2.o(x$fpl$printf2))
<BR><BR>[Stack]<UL><LI>Max Depth = 112<LI>Call Chain = _printf_fp_hex &rArr; _printf_fp_hex_real &rArr; _printf_fp_infnan &rArr; _printf_post_padding
</UL>
<BR>[Calls]<UL><LI><a href="#[9a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_printf_fp_hex_real
</UL>
<BR>[Called By]<UL><LI><a href="#[72]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_printf_a
</UL>

<P><STRONG><a name="[163]"></a>__fpl_return_NaN</STRONG> (Thumb, 100 bytes, Stack size 0 bytes, retnan.o(x$fpl$retnan))
<BR><BR>[Calls]<UL><LI><a href="#[16f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__fpl_cmpreturn
</UL>
<BR>[Called By]<UL><LI><a href="#[162]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__fpl_dcheck_NaN1
</UL>

<P><STRONG><a name="[155]"></a>__ARM_scalbn</STRONG> (Thumb, 92 bytes, Stack size 0 bytes, scalbn.o(x$fpl$scalbn))
<BR><BR>[Calls]<UL><LI><a href="#[162]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__fpl_dcheck_NaN1
</UL>
<BR>[Called By]<UL><LI><a href="#[129]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__hardfp_pow
</UL>

<P><STRONG><a name="[16f]"></a>__fpl_cmpreturn</STRONG> (Thumb, 48 bytes, Stack size 0 bytes, trapv.o(x$fpl$trapveneer))
<BR><BR>[Called By]<UL><LI><a href="#[163]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__fpl_return_NaN
</UL>
<P>
<H3>
Local Symbols
</H3>
<P><STRONG><a name="[124]"></a>PS2_GPIO_Init</STRONG> (Thumb, 44 bytes, Stack size 16 bytes, drive_ps2.o(i.PS2_GPIO_Init))
<BR><BR>[Stack]<UL><LI>Max Depth = 36<LI>Call Chain = PS2_GPIO_Init &rArr; GPIO_Init
</UL>
<BR>[Calls]<UL><LI><a href="#[c6]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;RCC_AHB1PeriphClockCmd
<LI><a href="#[c7]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;GPIO_Init
</UL>
<BR>[Called By]<UL><LI><a href="#[10c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;PS2_Keyboard_Init
</UL>

<P><STRONG><a name="[125]"></a>PS2_SCL_Set</STRONG> (Thumb, 82 bytes, Stack size 16 bytes, drive_ps2.o(i.PS2_SCL_Set))
<BR><BR>[Stack]<UL><LI>Max Depth = 36<LI>Call Chain = PS2_SCL_Set &rArr; GPIO_Init
</UL>
<BR>[Calls]<UL><LI><a href="#[c6]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;RCC_AHB1PeriphClockCmd
<LI><a href="#[c7]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;GPIO_Init
<LI><a href="#[12b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;GPIO_ResetBits
<LI><a href="#[12a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;GPIO_SetBits
</UL>
<BR>[Called By]<UL><LI><a href="#[dd]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;PS2_ReadKeyCodon
<LI><a href="#[126]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;PS2_SCL_Wait
</UL>

<P><STRONG><a name="[126]"></a>PS2_SCL_Wait</STRONG> (Thumb, 126 bytes, Stack size 8 bytes, drive_ps2.o(i.PS2_SCL_Wait))
<BR><BR>[Stack]<UL><LI>Max Depth = 44<LI>Call Chain = PS2_SCL_Wait &rArr; PS2_SCL_Set &rArr; GPIO_Init
</UL>
<BR>[Calls]<UL><LI><a href="#[127]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;GPIO_ReadInputDataBit
<LI><a href="#[125]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;PS2_SCL_Set
</UL>
<BR>[Called By]<UL><LI><a href="#[dd]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;PS2_ReadKeyCodon
</UL>

<P><STRONG><a name="[157]"></a>crc_16</STRONG> (Thumb, 58 bytes, Stack size 12 bytes, drive_communication.o(i.crc_16))
<BR><BR>[Stack]<UL><LI>Max Depth = 12<LI>Call Chain = crc_16
</UL>
<BR>[Called By]<UL><LI><a href="#[a5]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;sendData
</UL>

<P><STRONG><a name="[158]"></a>usartSendData</STRONG> (Thumb, 42 bytes, Stack size 16 bytes, drive_communication.o(i.usartSendData))
<BR><BR>[Stack]<UL><LI>Max Depth = 16<LI>Call Chain = usartSendData
</UL>
<BR>[Calls]<UL><LI><a href="#[159]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;USART_SendData
<LI><a href="#[15a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;USART_GetFlagStatus
</UL>
<BR>[Called By]<UL><LI><a href="#[a5]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;sendData
</UL>

<P><STRONG><a name="[137]"></a>SetSysClock</STRONG> (Thumb, 220 bytes, Stack size 12 bytes, system_stm32f4xx.o(i.SetSysClock))
<BR><BR>[Stack]<UL><LI>Max Depth = 12<LI>Call Chain = SetSysClock
</UL>
<BR>[Called By]<UL><LI><a href="#[60]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SystemInit
</UL>

<P><STRONG><a name="[16b]"></a>_dadd1</STRONG> (Thumb, 0 bytes, Stack size unknown bytes, daddsub_clz.o(x$fpl$dadd), UNUSED)
<BR><BR>[Called By]<UL><LI><a href="#[16c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_dsub
<LI><a href="#[16a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_drsb
</UL>

<P><STRONG><a name="[160]"></a>_dsub1</STRONG> (Thumb, 0 bytes, Stack size unknown bytes, daddsub_clz.o(x$fpl$dsub), UNUSED)
<BR><BR>[Called By]<UL><LI><a href="#[16a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_drsb
<LI><a href="#[15f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_dadd
</UL>

<P><STRONG><a name="[8f]"></a>_fp_digits</STRONG> (Thumb, 432 bytes, Stack size 96 bytes, _printf_fp_dec.o(.text))
<BR><BR>[Stack]<UL><LI>Max Depth = 220<LI>Call Chain = _fp_digits &rArr; _btod_etento &rArr; _btod_emul &rArr; _e2e
</UL>
<BR>[Calls]<UL><LI><a href="#[93]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_btod_emul
<LI><a href="#[92]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_btod_ediv
<LI><a href="#[91]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_btod_d2e
<LI><a href="#[90]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_btod_etento
<LI><a href="#[94]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_ll_udiv10
</UL>
<BR>[Called By]<UL><LI><a href="#[95]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_printf_fp_dec_real
</UL>

<P><STRONG><a name="[63]"></a>_printf_input_char</STRONG> (Thumb, 10 bytes, Stack size 0 bytes, _printf_char_common.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> _printf_char_common.o(.text)
</UL><P>
<H3>
Undefined Global Symbols
</H3><HR></body></html>
