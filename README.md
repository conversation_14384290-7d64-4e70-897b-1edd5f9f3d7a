# STM32F407ZG嵌入式系统工程框架

## 项目概述

本项目是基于STM32F407ZG微控制器的嵌入式系统工程框架，采用自定义RTOS实现，集成了完整的硬件驱动、系统服务和应用层功能。

## 系统架构

```
├── _01_App/           # 应用层
├── _02_Core/          # 系统核心(CMSIS、启动文件)
├── _03_Drive/         # 硬件驱动层
├── _04_FWLib/         # STM32固件库
├── _05_Os/            # 自定义操作系统
├── _06_System/        # 系统服务(延时、串口等)
├── _07_TFT_LCD/       # TFT液晶显示驱动
└── _08_USB/           # USB功能模块
```

## 核心特性

### 🕐 1ms时间基准系统
- **SysTick定时器**: 1ms系统时间片，提供精确时间基准
- **多层次延时**: 支持微秒(μs)、毫秒(ms)、秒(s)级延时
- **硬件定时器**: 14个定时器支持不同频率的定时控制
- **OS任务调度**: 基于1ms时间片的抢占式任务调度

### 🔧 硬件驱动支持
- **GPIO控制**: 通用输入输出控制
- **ADC/DAC**: 模数/数模转换
- **SPI/I2C**: 串行通信接口
- **CAN总线**: 控制器局域网通信
- **PWM输出**: 脉宽调制信号生成
- **触摸屏**: 电阻式触摸屏驱动

### 📱 外设功能
- **TFT液晶屏**: 彩色图形显示
- **PS2键盘**: 键盘输入支持
- **USB通信**: USB主机/设备功能
- **Flash存储**: 外部Flash存储管理
- **温度传感器**: DS18B20温度检测

## 时间配置分析

### 📊 1ms配置汇总报告
本项目包含详细的**1ms时间配置分析报告**，位于：
```
1ms_Configuration_Analysis_Report.md
```

该报告提供了：
- ✅ **完整配置清单**: 23个关键时间配置项
- ✅ **依赖关系图**: 配置项之间的依赖关系
- ✅ **兼容性分析**: 各模块时间配置兼容性评估
- ✅ **优化建议**: 系统时间配置优化方案
- ✅ **修改指南**: 配置变更的影响分析和操作步骤

### 🎯 核心时间配置
| 配置项 | 数值 | 说明 |
|--------|------|------|
| SystemCoreClock | 168MHz | 系统核心时钟 |
| System_Ticks | 1000Hz | 1ms系统时间片 |
| SysTick重载值 | 21000 | 1ms中断周期 |
| APB1时钟 | 42MHz | 外设时钟1 |
| APB2时钟 | 84MHz | 外设时钟2 |

## 开发环境

### 必需工具
- **Keil MDK-ARM**: v5.x或更高版本
- **STM32CubeMX**: 用于引脚配置(可选)
- **J-Link调试器**: 用于程序下载和调试

### 编译配置
- **目标芯片**: STM32F407ZGT6
- **编译器**: ARM Compiler v5.06
- **优化等级**: -O0 (调试版本)
- **浮点单元**: 启用FPU支持

## 快速开始

### 1. 环境准备
```bash
# 克隆项目
git clone [项目地址]

# 打开Keil工程
# 双击 _04.uvprojx 文件
```

### 2. 编译项目
1. 打开Keil MDK-ARM
2. 加载工程文件 `_04.uvprojx`
3. 选择目标配置
4. 点击编译按钮(F7)

### 3. 下载调试
1. 连接J-Link调试器
2. 配置调试设置
3. 点击下载按钮
4. 启动调试会话

## 系统配置

### 时钟配置
- **外部晶振**: 8MHz HSE
- **PLL配置**: 8MHz × 42 ÷ 2 = 168MHz
- **AHB分频**: 1 (HCLK = 168MHz)
- **APB1分频**: 4 (PCLK1 = 42MHz)
- **APB2分频**: 2 (PCLK2 = 84MHz)

### 内存配置
- **Flash**: 1MB程序存储
- **SRAM**: 192KB数据存储
- **CCM**: 64KB核心耦合内存

### 中断优先级
- **SysTick**: 优先级15 (最低)
- **定时器中断**: 优先级1-5
- **通信中断**: 优先级2-4
- **外部中断**: 优先级3-6

## 功能模块

### 操作系统(_05_Os/)
- **任务管理**: 支持多任务抢占式调度
- **内存管理**: 动态内存分配
- **时间管理**: 基于SysTick的时间服务
- **任务同步**: 信号量、互斥锁等同步机制

### 驱动层(_03_Drive/)
- **基础驱动**: GPIO、SPI、I2C、USART等
- **传感器驱动**: ADS1256、DS18B20、TMP275等
- **执行器驱动**: DAC、PWM、步进电机等
- **通信驱动**: CAN、PS2、触摸屏等

### 应用层(_01_App/)
- **LED控制**: LED状态指示和控制
- **触摸应用**: 触摸屏交互功能
- **用户接口**: 人机交互界面

## 注意事项

### ⚠️ 时间配置修改
在修改任何时间相关配置前，请务必：
1. 📖 **阅读分析报告**: 查看`1ms_Configuration_Analysis_Report.md`
2. 🔍 **评估影响范围**: 确认修改对其他模块的影响
3. 🧪 **充分测试**: 验证修改后的系统功能
4. 📝 **记录变更**: 使用报告中的变更记录模板

### 🚨 已知问题
- **USB延时不兼容**: USB模块使用独立延时实现，建议统一为系统延时
- **延时函数混用**: TFT_LCD模块存在`delay_ms()`和`Delay_ms()`混用
- **配置分散**: 时间相关配置分布在多个文件中

### 💡 优化建议
- 统一延时函数接口
- 集中时间配置管理
- 标准化时间精度等级
- 建立配置验证机制

## 技术支持

### 文档资源
- 📋 **配置分析报告**: `1ms_Configuration_Analysis_Report.md`
- 📖 **STM32参考手册**: STM32F407xx Reference Manual
- 🔧 **Keil用户指南**: MDK-ARM User Guide

### 联系方式
- **技术支持**: [技术支持邮箱]
- **问题反馈**: [GitHub Issues]
- **开发团队**: [团队联系方式]

## 版本历史

### v1.0 (当前版本)
- ✅ 完整的STM32F407ZG工程框架
- ✅ 自定义RTOS实现
- ✅ 多种硬件驱动支持
- ✅ 1ms时间配置分析报告
- ✅ USB、TFT_LCD、PS2等功能模块

### 计划功能
- 🔄 USB延时系统优化
- 🔄 配置管理工具
- 🔄 自动化测试框架
- 🔄 性能监控机制

## 许可证

本项目采用 [许可证类型] 许可证，详见 LICENSE 文件。

---
**项目维护**: STM32F407ZG开发团队  
**最后更新**: 2025-08-01  
**文档版本**: v1.0
